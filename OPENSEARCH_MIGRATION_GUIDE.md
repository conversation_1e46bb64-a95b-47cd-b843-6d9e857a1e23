# OpenSearch Migration Guide

## Overview

This guide documents the migration from the custom LMDB-backed BM25 + vector search system to OpenSearch native hybrid search in `backend_api3.py`.

## Migration Summary

### What Changed

**Before (LMDB System):**
- Custom `LMDBBackedBM25` class for lexical search
- Memory-mapped embeddings for vector search
- Manual hybrid scoring combination
- Disk-based cache management
- Complex memory management and garbage collection

**After (OpenSearch System):**
- OpenSearch native hybrid search
- Built-in BM25 and vector similarity
- Automatic relevance scoring
- Distributed search capabilities
- Simplified resource management

### Key Benefits

1. **Better Search Quality**: OpenSearch's mature search algorithms
2. **Scalability**: Distributed search with cluster architecture
3. **Maintenance**: Reduced complexity and better reliability
4. **Features**: Advanced query capabilities and filtering
5. **Performance**: Optimized indexing and search operations

## Technical Changes

### 1. Dependencies Added
```python
from opensearchpy import OpenSearch, RequestsHttpConnection
from opensearchpy.helpers import bulk
import pandas as pd
```

### 2. Configuration
```python
OPENSEARCH_CONFIG = {
    'hosts': [{'host': 'localhost', 'port': 9200}],
    'http_compress': True,
    'use_ssl': False,
    'connection_class': RequestsHttpConnection,
    'timeout': 30,
    'max_retries': 3,
    'retry_on_timeout': True,
    'http_auth': ("admin", "Summerist.l023")
}

INDEX_NAME = "reportnlp_hybrid"
EMBEDDING_DIM = 384
```

### 3. Core Components

#### OpenSearchEngine Class
Replaces `LMDBBackedBM25` with OpenSearch-based search:
- `search()`: BM25 lexical search
- `hybrid_search()`: Combined BM25 + vector search
- Proper resource management

#### Data Migration
- `load_and_index_data_to_opensearch()`: Migrates SQL Server data to OpenSearch
- Chunked processing for large datasets
- Proper error handling and progress tracking

#### Search Function
- `opensearch_hybrid_search()`: New search implementation
- Maintains existing API contract
- Uses OpenSearch native hybrid queries

### 4. API Endpoints

#### Existing Endpoints (Maintained)
- `POST /search`: Hybrid search (now uses OpenSearch)
- `GET /search`: Simple search interface
- `GET /memory`: Memory usage statistics
- `POST /gc`: Garbage collection

#### New OpenSearch Endpoints
- `GET /opensearch/health`: Cluster health and index status
- `POST /opensearch/reindex`: Manual data reindexing
- `DELETE /opensearch/cleanup`: Resource cleanup

## Deployment Steps

### 1. Prerequisites
Ensure OpenSearch cluster is running:
```bash
docker-compose up -d
```

### 2. Install Dependencies
```bash
pip install opensearch-py pandas
```

### 3. Start the Migrated API
```bash
python backend_api3.py
```

The system will:
1. Initialize OpenSearch connection
2. Create index if needed
3. Load data from SQL Server to OpenSearch
4. Start the API server

### 4. Validate Migration
```bash
python test_opensearch_migration.py
```

## API Compatibility

### Request Format (Unchanged)
```json
{
    "query": "search text",
    "top_k": 10,
    "semantic_weight": 0.7,
    "lexical_weight": 0.3,
    "min_similarity": 0.1,
    "include_scores": true
}
```

### Response Format (Maintained)
```json
{
    "success": true,
    "query": "search text",
    "total_results": 5,
    "execution_time_ms": 45.2,
    "results": [...],
    "search_parameters": {...}
}
```

## Performance Considerations

### Memory Usage
- **Reduced RAM requirements**: OpenSearch handles indexing
- **Better garbage collection**: Simplified memory management
- **Distributed storage**: Data stored in OpenSearch cluster

### Search Performance
- **Faster queries**: Native OpenSearch optimizations
- **Better relevance**: Mature scoring algorithms
- **Scalability**: Horizontal scaling with cluster

### Data Loading
- **One-time migration**: Data loaded once to OpenSearch
- **Incremental updates**: Can be handled through OpenSearch APIs
- **Backup/Recovery**: Standard OpenSearch procedures

## Monitoring and Maintenance

### Health Checks
```bash
curl http://localhost:8000/opensearch/health
```

### Reindexing
```bash
curl -X POST http://localhost:8000/opensearch/reindex
```

### Resource Cleanup
```bash
curl -X DELETE http://localhost:8000/opensearch/cleanup
```

## Troubleshooting

### Common Issues

1. **OpenSearch Connection Failed**
   - Check Docker containers: `docker ps`
   - Verify network connectivity
   - Check authentication credentials

2. **Index Creation Failed**
   - Check OpenSearch logs
   - Verify mapping configuration
   - Ensure sufficient disk space

3. **Search Performance Issues**
   - Monitor cluster health
   - Check index statistics
   - Consider index optimization

### Logs
Monitor application logs for:
- OpenSearch connection status
- Data indexing progress
- Search performance metrics
- Error messages and stack traces

## Rollback Plan

If needed, the original LMDB system can be restored by:
1. Reverting to the previous version of `backend_api3.py`
2. Ensuring LMDB cache files are available
3. Restarting the application

## Next Steps

1. **Performance Tuning**: Optimize OpenSearch settings
2. **Monitoring**: Set up cluster monitoring
3. **Backup Strategy**: Implement regular index backups
4. **Security**: Configure SSL and authentication
5. **Scaling**: Add more nodes as needed

## Support

For issues or questions:
1. Check OpenSearch documentation
2. Review application logs
3. Test with the validation script
4. Monitor cluster health and performance
