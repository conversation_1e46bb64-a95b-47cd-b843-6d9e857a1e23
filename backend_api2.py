print(f"\nInitializing Hybrid Search API server...", flush=True)

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, field_validator, model_validator
from sentence_transformers import SentenceTransformer
from sqlalchemy import create_engine, text as sa_text, exc
import connectorx as cx
import logging
import time
import traceback
from typing import Optional, List, Dict, Any
import uvicorn
from contextlib import asynccontextmanager
import numpy as np
import os
import re
import math
import psutil
import gc
import pickle
import joblib
import threading
import sys
import warnings
import lmdb
import json
import shutil

warnings.simplefilter(action="ignore", category=FutureWarning)



# Configuration for destination DB
srv_name2 = "HQDCSMOSQL01"
db_name2 = "PA_DEV2"
usr_name2 = "PAadmin"
pw2 = "PAadmin"
query_table = "ReportNLP"

# Connection URL for destination DB
connection_url2 = (
    "mssql+pyodbc://"
    f"{usr_name2}:{pw2}@{srv_name2}/{db_name2}"
    "?driver=ODBC+Driver+17+for+SQL+Server"
)


# Global variables for cache update monitoring
cache_update_thread = None
cache_update_running = False
cache_update_config = {
    'auto_update_enabled': False,  # DISABLED due to large dataset causing LMDB value size issues
    'check_interval_seconds': 99999999999,  # Interval in seconds for Incremental cache update check
    'batch_size': 20000,  # Same as full cache build
    'max_memory_growth_mb': 10240,  # Increased for comprehensive updates that may find many missing records
    'max_incremental_records': 100000,  # Disable incremental updates for very large datasets
    'enable_chunked_posting_lists': True,  # Enable chunked storage for large posting lists
}
# Note: TOP clause conflicts with OFFSET, so chunking is disabled when TOP is used
TOP_CLAUSE = ""  # CHANGE TO "" TO REMOVE PROC LIMIT


ENGLISH_STOPWORDS = {
    'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', "you're", "you've", "you'll", "you'd", 'your',
    'yours', 'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', "she's", 'her', 'hers', 'herself', 'it',
    "it's", 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which', 'who', 'whom', 'this',
    'that', "that'll", 'these', 'those', 'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
    'having', 'do', 'does', 'did', 'doing', 'a', 'an', 'the', 'and', 'but', 'if', 'or', 'because', 'as', 'until', 'while',
    'of', 'at', 'by', 'for', 'with', 'about', 'against', 'between', 'into', 'through', 'during', 'before', 'after',
    'above', 'below', 'to', 'from', 'up', 'down', 'in', 'out', 'on', 'off', 'over', 'under', 'again', 'further', 'then',
    'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other',
    'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will',
    'just', 'don', "don't", 'should', "should've", 'now', 'd', 'll', 'm', 'o', 're', 've', 'y', 'ain', 'aren', "aren't",
    'couldn', "couldn't", 'didn', "didn't", 'doesn', "doesn't", 'hadn', "hadn't", 'hasn', "hasn't", 'haven', "haven't",
    'isn', "isn't", 'ma', 'mightn', "mightn't", 'mustn', "mustn't", 'needn', "needn't", 'shan', "shan't", 'shouldn',
    "shouldn't", 'wasn', "wasn't", 'weren', "weren't", 'won', "won't", 'wouldn', "wouldn't"
}


# Global variables for model and database
model = None
engine = None
search_cache = None
cache_update_in_progress = False  # Prevents overlap between full/incremental builds

# Global variables for system monitoring
monitor_thread = None
monitor_running = False
current_monitor_status = ""


# Custom logging handler that coordinates with the monitor
class MonitorAwareStreamHandler(logging.StreamHandler):
    def __init__(self, stream=None):
        super().__init__(stream)

    def emit(self, record):
        try:
            # Format the message
            msg = self.format(record)

            # Clear monitor line, print message, then restore monitor
            if monitor_running and current_monitor_status:
                # Save cursor, move to bottom, clear line
                sys.stdout.write("\033[s")  # Save cursor
                sys.stdout.write("\033[999;1H")  # Move to bottom
                sys.stdout.write("\033[2K")  # Clear line
                sys.stdout.write("\033[u")  # Restore cursor

                # Print the log message normally
                sys.stdout.write(msg + '\n')
                sys.stdout.flush()

                # Immediately redraw the monitor status
                sys.stdout.write("\033[s")  # Save cursor
                sys.stdout.write("\033[999;1H")  # Move to bottom
                sys.stdout.write("\033[2K")  # Clear line
                sys.stdout.write(current_monitor_status)  # Write status
                sys.stdout.write("\033[u")  # Restore cursor
                sys.stdout.flush()
            else:
                # Normal logging when monitor is not running
                sys.stdout.write(msg + '\n')
                sys.stdout.flush()

        except Exception:
            self.handleError(record)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('search_api.log'),
        MonitorAwareStreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def get_memory_usage():
    # Get current memory usage in MB
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def force_garbage_collection():
    # Force garbage collection and return memory freed
    before = get_memory_usage()

    # Multiple GC passes for better cleanup
    collected_objects = 0
    for i in range(3):  # Multiple passes
        collected = gc.collect()
        collected_objects += collected
        if collected == 0:
            break  # No more objects to collect

    after = get_memory_usage()
    freed = before - after

    # Log significant memory operations
    if freed > 5 or collected_objects > 1000:
        logger.info(f"GC: {collected_objects} objects collected, {freed:.1f}MB freed")

    return freed


# Text normalization and tokenization
def normalize_and_tokenize(text: str) -> list:
    if not text:
        return []
    norm_text = re.sub(r'\s+', ' ', text.replace('\n', ' ')).strip()
    tokens = [t for t in re.findall(r'\b\w+\b', norm_text.lower()) if t not in ENGLISH_STOPWORDS]
    return tokens


def system_monitor_worker():
    # Background worker for system monitoring display with persistent bottom status bar
    global monitor_running, current_monitor_status

    pid = os.getpid()

    # Terminal control sequences
    SAVE_CURSOR = "\033[s"
    RESTORE_CURSOR = "\033[u"
    MOVE_TO_BOTTOM = "\033[999;1H"  # Move to bottom of terminal
    CLEAR_LINE = "\033[2K"
    HIDE_CURSOR = "\033[?25l"
    SHOW_CURSOR = "\033[?25h"

    try:
        time.sleep(4)
        # Hide cursor
        sys.stdout.write(HIDE_CURSOR)
        sys.stdout.flush()

        while monitor_running:
            try:
                process = psutil.Process(pid)
                memory_mb = process.memory_info().rss / (1024 * 1024)
                cpu_percent = process.cpu_percent()

                # Get cache info if available
                cache_info = ""
                if search_cache:
                    cache_size = len(search_cache['ids'])
                    cache_from_disk = search_cache.get('loaded_from_existing_cache', False)
                    cache_source = "disk" if cache_from_disk else "db"
                    cache_info = f" | Cache: {cache_size:,} records"

                # Create status line
                status_line = f"[MONITOR] PID {pid}: {memory_mb:.1f}MB RAM, CPU: {cpu_percent:.1f}%{cache_info}"
                current_monitor_status = status_line

                # More robust monitor display - always save cursor, move to bottom, clear line, write status, restore
                sys.stdout.write(SAVE_CURSOR)
                sys.stdout.write(MOVE_TO_BOTTOM)
                sys.stdout.write(CLEAR_LINE)
                sys.stdout.write(status_line)
                sys.stdout.write(RESTORE_CURSOR)
                sys.stdout.flush()

                time.sleep(5)  # Update every 5 seconds

            except psutil.NoSuchProcess:
                break
            except Exception as e:
                # Don't let monitoring errors crash the app
                time.sleep(5)
                continue

    except Exception:
        pass
    finally:
        # Clear the status line and show cursor when stopping
        sys.stdout.write(SAVE_CURSOR)
        sys.stdout.write(MOVE_TO_BOTTOM)
        sys.stdout.write(CLEAR_LINE)
        sys.stdout.write(RESTORE_CURSOR)
        sys.stdout.write(SHOW_CURSOR)
        sys.stdout.flush()
        current_monitor_status = ""

def setup_terminal_for_monitor():
    # Set up terminal for persistent bottom status bar
    try:
        # Reserve space at bottom by moving cursor up (no extra newlines)
        sys.stdout.write("\033[3A")  # Move cursor up 3 lines
        sys.stdout.flush()

        # No print/log for monitor setup
        sys.stdout.flush()
    except Exception:
        pass  # Ignore terminal setup errors

def start_system_monitor():
    # Start the system monitoring thread
    global monitor_thread, monitor_running

    if monitor_thread is None or not monitor_thread.is_alive():
        setup_terminal_for_monitor()
        monitor_running = True
        monitor_thread = threading.Thread(target=system_monitor_worker, daemon=True)
        monitor_thread.start()
        # Ensure log is on a new line, not appended to monitor status or previous output
        logger.info(f"\nSystem monitor started\n")

def stop_system_monitor():
    # Stop the system monitoring thread
    global monitor_running

    monitor_running = False
    if monitor_thread and monitor_thread.is_alive():
        monitor_thread.join(timeout=1)
        logger.info("System monitor stopped")

def cache_update_monitor_worker():
    # Background worker for automatic cache updates
    global cache_update_running, cache_update_in_progress

    logger.info("Cache update monitor started")

    while cache_update_running:
        try:
            if cache_update_config.get('auto_update_enabled', True):
                if cache_update_in_progress:
                    logger.info("Cache update in progress (full/incremental build), skipping incremental update check.")
                else:
                    cache_update_in_progress = True
                    try:
                        update_result = perform_incremental_cache_update()

                        if update_result['success'] and update_result.get('new_records_added', 0) > 0:
                            added = update_result.get('missing_records_added', 0)
                            total = update_result.get('total_records', 0)
                            process_time = update_result.get('total_process_time_seconds', 0)
                            logger.info(f"Automatic cache update completed: +{added:,} records, total: {total:,}, time: {process_time:.1f}s")
                        elif not update_result['success'] and update_result.get('reason') not in ['No new records', 'No valid new records']:
                            logger.warning(f"Automatic cache update failed: {update_result.get('reason', 'Unknown error')}")
                    finally:
                        cache_update_in_progress = False

            # Wait for next check
            check_interval = cache_update_config.get('check_interval_seconds', 300)
            time.sleep(check_interval)

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Cache update monitor error: {error_msg}")
            
            # Handle specific error types
            if "timestamp too large" in error_msg or "_PyTime_t" in error_msg:
                logger.error("Timestamp overflow detected - cache update monitor will use safe timestamps")
            elif "MDB_BAD_VALSIZE" in error_msg:
                logger.error("LMDB value size limit exceeded - cache update may need chunking")
            elif "Cache update failed" in error_msg:
                logger.error("Cache update operation failed - monitor will retry with longer interval")
                time.sleep(300)  # Wait 5 minutes before retrying after cache update failure
                continue
            
            # Continue running even if there's an error, but wait before retrying
            time.sleep(60)  # Wait 1 minute before retrying

    logger.info("Cache update monitor stopped")

def start_cache_update_monitor():
    # Start the cache update monitoring thread
    global cache_update_thread, cache_update_running

    if cache_update_config.get('auto_update_enabled', True):
        if cache_update_thread is None or not cache_update_thread.is_alive():
            cache_update_running = True
            cache_update_thread = threading.Thread(target=cache_update_monitor_worker, daemon=True)
            cache_update_thread.start()
    else:
        logger.info("Cache update monitor disabled by configuration")

def stop_cache_update_monitor():
    # Stop the cache update monitoring thread
    global cache_update_running

    cache_update_running = False
    if cache_update_thread and cache_update_thread.is_alive():
        cache_update_thread.join(timeout=2)
        logger.info("Cache update monitor stopped")

class LMDBBackedBM25:
    # LMDB-backed BM25+ implementation for large document collections
    # Stores per-document term frequencies and inverted index in LMDB for minimal RAM usage
    # Uses BM25+ algorithm which improves upon standard BM25 with better term frequency saturation
    def __init__(self, cache_dir: str = "search_cache", k1: float = 1.5, b: float = 0.75, delta: float = 1.0):
        self.cache_dir = cache_dir
        self.k1 = k1
        self.b = b
        self.delta = delta  # BM25+ parameter: prevents score degradation for repeated terms
        self.lmdb_path = f"{cache_dir}/bm25.lmdb"

        # LMDB environment (opened once, reused for all operations)
        self.env = None
        self.doc_count = 0
        self.avg_doc_length = 0

        os.makedirs(cache_dir, exist_ok=True)
        self._open_lmdb_env()

    def _open_lmdb_env(self):
        # Open LMDB environment with appropriate map size for scalability
        if self.env is None:
            # Use smaller map size for testing, larger for production
            map_size = 10 * 1024 * 1024 * 1024  # 10GB LMDB map size
            if "search_cache" in self.cache_dir and "test" not in self.cache_dir:
                map_size = 10 * 1024 * 1024 * 1024  # 10GB for production

            self.env = lmdb.open(
                self.lmdb_path,
                map_size=map_size,
                max_dbs=0,  # Single database
                readonly=False,
                writemap=True,  # Use writemap for better performance on Windows
                map_async=True,   # Async writes for better performance
                max_spare_txns=1,  # Limit spare transactions
                meminit=False,  # Don't initialize malloc'd memory
                max_readers=1024  # Increase max readers
            )

    def _process_text_chunk(self, ids, texts):
        # Build inverted index and doc lengths using LMDB
        # Get the actual next document index from LMDB
        start_idx = self._get_next_doc_index()
        # logger.debug(f"Processing {len(texts)} texts starting at index {start_idx}")

        with self.env.begin(write=True) as txn:
            for i, text in enumerate(texts):
                doc_idx = start_idx + i
                tokens = normalize_and_tokenize(text)
                doc_length = len(tokens)

                # Store document length
                doc_len_key = f"doclen:{doc_idx}".encode('utf-8')
                txn.put(doc_len_key, str(doc_length).encode('utf-8'))
                # logger.debug(f"Stored doclen:{doc_idx} = {doc_length}")

                # Count term frequencies
                tf_counter = {}
                for token in tokens:
                    tf_counter[token] = tf_counter.get(token, 0) + 1

                # Update inverted index for each unique token
                for token, tf in tf_counter.items():
                    try:
                        self._update_posting_list_chunked(txn, token, doc_idx, tf)
                    except Exception as e:
                        if "MDB_BAD_VALSIZE" in str(e):
                            logger.warning(f"Value too large for token '{token}', attempting chunked storage")
                            self._update_posting_list_chunked(txn, token, doc_idx, tf, force_chunk=True)
                        else:
                            raise

        # Update document count
        self.doc_count += len(texts)

    def _finalize_build(self, total_docs):
        # Calculate and store metadata in LMDB
        self.doc_count = total_docs

        # Calculate average document length from LMDB
        total_length = 0
        with self.env.begin() as txn:
            cursor = txn.cursor()

            doc_count = 0
            # Iterate through all keys
            if cursor.first():
                while True:
                    key = cursor.key().decode('utf-8')
                    if key.startswith('doclen:'):
                        doc_length = int(cursor.value().decode('utf-8'))
                        total_length += doc_length
                        doc_count += 1

                    if not cursor.next():
                        break

        self.avg_doc_length = total_length / doc_count if doc_count > 0 else 0

        # Store metadata in LMDB
        with self.env.begin(write=True) as txn:
            txn.put(b'meta:doc_count', str(self.doc_count).encode('utf-8'))
            txn.put(b'meta:avg_doc_length', str(self.avg_doc_length).encode('utf-8'))
            txn.put(b'meta:k1', str(self.k1).encode('utf-8'))
            txn.put(b'meta:b', str(self.b).encode('utf-8'))
            txn.put(b'meta:delta', str(self.delta).encode('utf-8'))
            txn.put(b'meta:algorithm', b'BM25+')

        logger.info(f"LMDB BM25+ index finalized: {self.doc_count} documents, avg_length={self.avg_doc_length:.1f}, delta={self.delta}")
        force_garbage_collection()

    def build_from_text_chunks(self, text_chunks_generator):
        logger.info("Building LMDB-backed BM25 index (full)...")
        processed_docs = 0
        for chunk_idx, (ids, texts) in enumerate(text_chunks_generator):
            logger.info(f"Processing BM25 chunk {chunk_idx + 1}: {len(texts)} documents")
            self._process_text_chunk(ids, texts)
            processed_docs += len(texts)
            if chunk_idx % 10 == 0:
                force_garbage_collection()
        self._finalize_build(processed_docs)

    def load_from_lmdb(self):
        # Load metadata from LMDB (no large data structures loaded into RAM)
        #logger.info("Loading BM25+ metadata from LMDB...")

        with self.env.begin() as txn:
            # Load document count
            doc_count_data = txn.get(b'meta:doc_count')
            if doc_count_data:
                self.doc_count = int(doc_count_data.decode('utf-8'))
            else:
                self.doc_count = 0

            # Load average document length
            avg_length_data = txn.get(b'meta:avg_doc_length')
            if avg_length_data:
                self.avg_doc_length = float(avg_length_data.decode('utf-8'))
            else:
                self.avg_doc_length = 0

            # Load BM25+ parameters (backward compatibility)
            delta_data = txn.get(b'meta:delta')
            if delta_data:
                self.delta = float(delta_data.decode('utf-8'))
            # If no delta stored, keep the default value from __init__

            # Load algorithm type for debugging
            algorithm_data = txn.get(b'meta:algorithm')
            algorithm_type = algorithm_data.decode('utf-8') if algorithm_data else 'BM25'

        logger.info(f"Loading BM25+ metadata from {self.cache_dir}/bm25.lmdb ({self.doc_count} documents, Avg # of Words: {self.avg_doc_length:.1f})")

    # Keep old method name for compatibility
    def load_from_disk(self):
        return self.load_from_lmdb()

    def add_documents_incremental(self, new_ids, new_texts):
        # Check if incremental update should be skipped for very large datasets
        max_incremental = cache_update_config.get('max_incremental_records', 100000)
        if len(new_texts) > max_incremental:
            logger.warning(f"Incremental update skipped: {len(new_texts)} records exceeds limit of {max_incremental}")
            logger.warning("Consider rebuilding cache from scratch for large updates")
            raise RuntimeError(f"Dataset too large for incremental update ({len(new_texts)} > {max_incremental})")
        
        logger.info(f"Adding {len(new_texts)} documents to LMDB BM25+ index incrementally...")

        if len(new_ids) != len(new_texts):
            raise ValueError("new_ids and new_texts must have the same length")

        # Get current state before processing
        old_doc_count = self.doc_count
        old_avg_length = self.avg_doc_length
        
        # Calculate total length from existing documents (for efficient avg calculation)
        old_total_length = old_doc_count * old_avg_length if old_doc_count > 0 else 0
        
        # Get the next available document index to avoid conflicts
        start_idx = self._get_next_doc_index()
        logger.info(f"Starting incremental addition at document index {start_idx}")

        # Process new documents
        new_total_length = 0
        processed_count = 0
        
        with self.env.begin(write=True) as txn:
            for i, text in enumerate(new_texts):
                doc_idx = start_idx + i
                tokens = normalize_and_tokenize(text)
                doc_length = len(tokens)
                
                # Check if this document index already exists (safety check)
                doc_len_key = f"doclen:{doc_idx}".encode('utf-8')
                existing_doc = txn.get(doc_len_key)
                if existing_doc is not None:
                    logger.warning(f"Document index {doc_idx} already exists, skipping to avoid overwrite")
                    continue

                # Store document length
                txn.put(doc_len_key, str(doc_length).encode('utf-8'))
                new_total_length += doc_length
                processed_count += 1

                # Count term frequencies
                tf_counter = {}
                for token in tokens:
                    tf_counter[token] = tf_counter.get(token, 0) + 1

                # Update inverted index for each unique token
                for token, tf in tf_counter.items():
                    try:
                        self._update_posting_list_chunked(txn, token, doc_idx, tf)
                    except Exception as e:
                        if "MDB_BAD_VALSIZE" in str(e):
                            logger.warning(f"Value too large for token '{token}', attempting chunked storage")
                            self._update_posting_list_chunked(txn, token, doc_idx, tf, force_chunk=True)
                        else:
                            raise

        if processed_count == 0:
            logger.warning("No new documents were processed (all indices already existed)")
            return

        # Update document count and average length efficiently
        new_doc_count = old_doc_count + processed_count
        combined_total_length = old_total_length + new_total_length
        new_avg_length = combined_total_length / new_doc_count if new_doc_count > 0 else 0
        
        # Update instance variables
        self.doc_count = new_doc_count
        self.avg_doc_length = new_avg_length

        # Update metadata in LMDB
        with self.env.begin(write=True) as txn:
            txn.put(b'meta:doc_count', str(self.doc_count).encode('utf-8'))
            txn.put(b'meta:avg_doc_length', str(self.avg_doc_length).encode('utf-8'))
            txn.put(b'meta:delta', str(self.delta).encode('utf-8'))
            txn.put(b'meta:algorithm', b'BM25+')
            # Add timestamp for debugging
            txn.put(b'meta:last_incremental_update', str(safe_timestamp()).encode('utf-8'))

        logger.info(f"LMDB BM25+ index updated: added {processed_count}/{len(new_texts)} documents")
        logger.info(f"Total documents: {old_doc_count} -> {self.doc_count}")
        logger.info(f"Average document length: {old_avg_length:.1f} -> {self.avg_doc_length:.1f}")
        
        force_garbage_collection()

    def _update_posting_list_chunked(self, txn, token, doc_idx, tf, force_chunk=False, max_chunk_size=400000):
        # Update posting list with chunking support for large values
        # LMDB has a practical limit around 511 bytes to a few KB for values
        # We'll chunk when JSON size approaches this limit
        
        token_key = f"token:{token}".encode('utf-8')
        doc_idx_str = str(doc_idx)
        
        if not force_chunk:
            # Try normal storage first
            try:
                existing_data = txn.get(token_key)
                if existing_data:
                    posting_list = json.loads(existing_data.decode('utf-8'))
                else:
                    posting_list = {}
                
                posting_list[doc_idx_str] = tf
                serialized = json.dumps(posting_list).encode('utf-8')
                
                # Check if it would fit (conservative limit)
                if len(serialized) < max_chunk_size:
                    txn.put(token_key, serialized)
                    return
                else:
                    # Too large, fall through to chunked storage
                    force_chunk = True
            except Exception as e:
                if "MDB_BAD_VALSIZE" in str(e):
                    force_chunk = True
                else:
                    raise
        
        if force_chunk:
            # Use chunked storage for large posting lists
            self._update_chunked_posting_list(txn, token, doc_idx, tf)

    def _update_chunked_posting_list(self, txn, token, doc_idx, tf, chunk_size=1000):
        # Store posting lists in chunks when they become too large
        doc_idx_str = str(doc_idx)
        
        # First, find which chunk this document should go in
        chunk_idx = 0
        while True:
            chunk_key = f"token:{token}:chunk{chunk_idx}".encode('utf-8')
            chunk_data = txn.get(chunk_key)
            
            if chunk_data is None:
                # Create new chunk
                chunk_posting_list = {doc_idx_str: tf}
                break
            else:
                chunk_posting_list = json.loads(chunk_data.decode('utf-8'))
                
                # Check if document already exists in this chunk
                if doc_idx_str in chunk_posting_list:
                    chunk_posting_list[doc_idx_str] = tf
                    break
                
                # Check if this chunk has room for one more entry
                if len(chunk_posting_list) < chunk_size:
                    chunk_posting_list[doc_idx_str] = tf
                    break
                
                # This chunk is full, try next chunk
                chunk_idx += 1
        
        # Store the updated chunk
        chunk_key = f"token:{token}:chunk{chunk_idx}".encode('utf-8')
        serialized = json.dumps(chunk_posting_list).encode('utf-8')
        txn.put(chunk_key, serialized)
        
        # Update chunk count metadata for this token
        chunk_count_key = f"token:{token}:chunks".encode('utf-8')
        max_chunks = max(chunk_idx + 1, int(txn.get(chunk_count_key, b'0').decode('utf-8')))
        txn.put(chunk_count_key, str(max_chunks).encode('utf-8'))

    def _get_chunked_posting_list(self, txn, token):
        # Retrieve complete posting list from chunked storage
        posting_list = {}
        
        # First try non-chunked storage
        token_key = f"token:{token}".encode('utf-8')
        regular_data = txn.get(token_key)
        if regular_data:
            try:
                posting_list.update(json.loads(regular_data.decode('utf-8')))
            except json.JSONDecodeError:
                logger.warning(f"Corrupted regular posting list for token '{token}'")
        
        # Then try chunked storage
        chunk_count_key = f"token:{token}:chunks".encode('utf-8')
        chunk_count_data = txn.get(chunk_count_key)
        
        if chunk_count_data:
            try:
                chunk_count = int(chunk_count_data.decode('utf-8'))
                for chunk_idx in range(chunk_count):
                    chunk_key = f"token:{token}:chunk{chunk_idx}".encode('utf-8')
                    chunk_data = txn.get(chunk_key)
                    if chunk_data:
                        try:
                            chunk_posting_list = json.loads(chunk_data.decode('utf-8'))
                            posting_list.update(chunk_posting_list)
                        except json.JSONDecodeError:
                            logger.warning(f"Corrupted chunk {chunk_idx} for token '{token}'")
            except ValueError:
                logger.warning(f"Invalid chunk count for token '{token}'")
        
        return posting_list

    def get_scores(self, query: str) -> np.ndarray:
        # LMDB-backed BM25+ scoring with improved document count handling
        current_doc_count = self._get_current_doc_count()
        if current_doc_count == 0:
            return np.array([], dtype=np.float32)
            
        scores = np.zeros(current_doc_count, dtype=np.float32)
        query_tokens = normalize_and_tokenize(query)
        unique_query_tokens = set(query_tokens)

        with self.env.begin() as txn:
            for token in unique_query_tokens:
                # Use chunked posting list retrieval
                posting_list = self._get_chunked_posting_list(txn, token)
                
                if not posting_list:
                    continue  # Token not found in index
                    
                df = len(posting_list)  # Document frequency

                # Calculate IDF using Robertson-Sparck Jones formula
                idf = math.log(1 + (current_doc_count - df + 0.5) / (df + 0.5))
                query_tf = query_tokens.count(token)

                # Score each document containing this token
                for doc_idx_str, tf in posting_list.items():
                    try:
                        doc_idx = int(doc_idx_str)
                        
                        # Ensure doc_idx is within bounds
                        if doc_idx >= current_doc_count:
                            logger.warning(f"Document index {doc_idx} exceeds current count {current_doc_count}")
                            continue
                            
                        # Get document length from LMDB
                        doc_len_key = f"doclen:{doc_idx}".encode('utf-8')
                        doc_len_data = txn.get(doc_len_key)
                        if doc_len_data:
                            doc_length = int(doc_len_data.decode('utf-8'))
                        else:
                            doc_length = self.avg_doc_length  # Fallback
                            logger.warning(f"Missing document length for doc {doc_idx}, using average")

                        # BM25+ scoring formula
                        length_norm = 1 - self.b + self.b * (doc_length / self.avg_doc_length)
                        tf_component = (tf * (self.k1 + 1)) / (tf + self.k1 * length_norm)
                        bm25_plus_score = idf * (self.delta + tf_component) * query_tf
                        
                        scores[doc_idx] += bm25_plus_score
                        
                    except (ValueError, IndexError) as e:
                        logger.warning(f"Error processing document {doc_idx_str} for token '{token}': {e}")
                        continue

        return scores

    def _get_current_doc_count(self):
        # Get current document count from LMDB with fallback (improved)
        with self.env.begin() as txn:
            # Try to get from metadata first
            doc_count_data = txn.get(b'meta:doc_count')
            if doc_count_data:
                try:
                    return int(doc_count_data.decode('utf-8'))
                except ValueError:
                    pass

            # Fallback: count doclen entries
            count = 0
            cursor = txn.cursor()

            if cursor.first():
                while True:
                    key = cursor.key().decode('utf-8')
                    if key.startswith('doclen:'):
                        count += 1

                    if not cursor.next():
                        break

            # Update metadata if it was missing
            if count > 0:
                # Use a separate transaction for metadata update
                with self.env.begin(write=True) as write_txn:
                    write_txn.put(b'meta:doc_count', str(count).encode('utf-8'))

            return count

    def _get_next_doc_index(self):
        # Get the next available document index (improved to handle gaps)
        max_idx = -1
        with self.env.begin() as txn:
            cursor = txn.cursor()

            # Iterate through all doclen keys to find the maximum index
            if cursor.first():
                while True:
                    key = cursor.key().decode('utf-8')
                    if key.startswith('doclen:'):
                        try:
                            doc_idx = int(key.split(':')[1])
                            max_idx = max(max_idx, doc_idx)
                        except (ValueError, IndexError):
                            # Skip malformed keys
                            continue

                    if not cursor.next():
                        break

        next_idx = max_idx + 1
        return next_idx

    def close(self):
        # Close LMDB environment
        if self.env:
            self.env.close()
            self.env = None

    def __del__(self):
        # Ensure LMDB environment is closed on object destruction
        self.close()

def load_data_chunked_and_split(cache_dir="search_cache", chunk_size= cache_update_config['batch_size']):
    # Load data in chunks and split into embeddings and text for separate processing
    global cache_update_in_progress
    logger.info("Loading data with chunked processing and splitting...")

    cache_update_in_progress = True
    try:
        # Get total count first (with TOP clause for debugging)
        with engine.connect() as conn:
            count_query = f"""
                SELECT COUNT(*) as total_count
                FROM (
                    SELECT {TOP_CLAUSE} Id
                    FROM ReportNLP
                    WHERE empty_report = 0
                      AND empty_embedding = 0
                      AND extracted_text IS NOT NULL
                      AND embedding_vector IS NOT NULL
                ) subquery
            """
            result = conn.execute(sa_text(count_query))
            total_count = result.fetchone()[0]

        logger.info(f"Total records to process: {total_count}")

        # Create cache directory and memory-mapped files
        os.makedirs(cache_dir, exist_ok=True)

        # Initialize memory-mapped arrays (assuming 384-dim embeddings)
        embeddings_mmap = np.memmap(
            f"{cache_dir}/embeddings.dat",
            dtype=np.float32,
            mode='w+',
            shape=(total_count, 384)
        )

        # Initialize BM25 builder
        bm25 = LMDBBackedBM25(cache_dir)

        ids_list = []
        processed = 0

        # Process in chunks - single query for both data types
        for offset in range(0, total_count, chunk_size):
            logger.info(f"Processing chunk {offset//chunk_size + 1}: records {offset}-{min(offset+chunk_size, total_count)}")

            # Single query to get both text and embeddings - handle TOP clause conflict with OFFSET
            if TOP_CLAUSE:
                # When using TOP, we can't use OFFSET, so just get the TOP records in first chunk
                if offset > 0:
                    # Skip subsequent chunks when using TOP clause
                    break

                query = f"""
                    SELECT {TOP_CLAUSE}
                    Id, extracted_text, embedding_vector
                    FROM ReportNLP
                    WHERE empty_report = 0
                      AND empty_embedding = 0
                      AND extracted_text IS NOT NULL
                      AND embedding_vector IS NOT NULL
                """
            else:
                # Without TOP clause, we can use OFFSET normally
                query = f"""
                    SELECT Id, extracted_text, embedding_vector
                    FROM ReportNLP
                    WHERE empty_report = 0
                      AND empty_embedding = 0
                      AND extracted_text IS NOT NULL
                      AND embedding_vector IS NOT NULL
                    ORDER BY Id
                    OFFSET {offset} ROWS FETCH NEXT {chunk_size} ROWS ONLY
                """

            df_chunk = cx.read_sql(
                connection_url2,
                query,
                return_type="pandas"
            )

            if df_chunk.empty:
                break

            # Split data for processing
            chunk_ids = []
            chunk_texts = []
            valid_count = 0

            for i, row in df_chunk.iterrows():
                row_id = str(row['Id'])
                text = row['extracted_text']
                embedding_str = row['embedding_vector']

                # Process embedding
                try:
                    if embedding_str:
                        embedding = np.fromstring(embedding_str, sep=',', dtype=np.float32)
                        if len(embedding) == 384:  # Validate dimension
                            embeddings_mmap[processed + valid_count] = embedding.astype(np.float32)
                            chunk_ids.append(row_id)
                            chunk_texts.append(text)
                            valid_count += 1
                except (ValueError, AttributeError) as e:
                    logger.warning(f"Failed to parse embedding for ID {row_id}: {e}")
                    continue

            # Process text chunk for BM25 (if we have valid data)
            if chunk_texts:
                # Process this chunk for BM25 building
                bm25._process_text_chunk(chunk_ids, chunk_texts)

            ids_list.extend(chunk_ids)
            processed += valid_count

            # Force cleanup after each chunk
            del df_chunk
            force_garbage_collection()

            if offset % (chunk_size * 5) == 0:  # Every 5 chunks
                logger.info(f"Progress: {processed}/{total_count} records processed ({100*processed/total_count:.1f}%)")

                # Additional aggressive GC during cache building
                current_memory = get_memory_usage()
                if current_memory > 1000:  # 1GB threshold for cache building
                    freed = force_garbage_collection()
                    logger.info(f"Cache-building GC freed {freed:.1f}MB (memory was {current_memory:.1f}MB)")

        # Finalize BM25 processing
        bm25._finalize_build(processed)

        # Resize mmap to actual processed count
        if processed < total_count:
            logger.info(f"Resizing embeddings array from {total_count} to {processed}")
            embeddings_mmap = np.memmap(
                f"{cache_dir}/embeddings.dat",
                dtype=np.float32,
                mode='r+',
                shape=(processed, 384)
            )

        # Save IDs to disk
        with open(f"{cache_dir}/ids.pkl", 'wb') as f:
            pickle.dump(ids_list, f)

        # Save cache metadata including latest Entered_Time
        try:
            # Get latest Entered_Time from the loaded records
            if ids_list:
                with engine.connect() as conn:
                    sample_ids = ids_list[-min(100, len(ids_list)):]  # Get last 100 IDs
                    placeholders = ','.join([f':id_{i}' for i in range(len(sample_ids))])
                    params = {f'id_{i}': sample_ids[i] for i in range(len(sample_ids))}

                    result = conn.execute(sa_text(f"""
                        SELECT MAX(Entered_Time) as max_entered_time
                        FROM ReportNLP
                        WHERE Id IN ({placeholders})
                    """), params)

                    latest_entered_time = result.fetchone()[0]
            else:
                latest_entered_time = None

            cache_metadata = {
                'record_count': processed,
                'latest_entered_time': latest_entered_time,
                'created_at': safe_timestamp(),
                'cache_version': '1.0'
            }

            with open(f"{cache_dir}/metadata.pkl", 'wb') as f:
                pickle.dump(cache_metadata, f)

        except Exception as e:
            logger.warning(f"Failed to save cache metadata: {str(e)}")

        logger.info(f"Data loading complete: {processed} records saved to {cache_dir}")

        return {
            'embeddings_file': f"{cache_dir}/embeddings.dat",
            'ids_file': f"{cache_dir}/ids.pkl",
            'count': processed,
            'shape': (processed, 384),
            'bm25': bm25
        }
    finally:
        cache_update_in_progress = False

def check_cache_files_exist(cache_dir="search_cache"):
    # Check if all required cache files exist (updated for LMDB)
    required_files = [
        f"{cache_dir}/embeddings.dat",
        f"{cache_dir}/ids.pkl",
        f"{cache_dir}/bm25.lmdb"  # LMDB database file
    ]

    for file_path in required_files:
        if not os.path.exists(file_path):
            logger.info(f"Cache file missing: {file_path}")
            return False
    return True

def get_cache_file_info(cache_dir="search_cache"):
    # Get information about existing cache files
    try:
        embeddings_file = f"{cache_dir}/embeddings.dat"
        ids_file = f"{cache_dir}/ids.pkl"

        # Get file sizes and modification times
        embeddings_stat = os.stat(embeddings_file)
        ids_stat = os.stat(ids_file)

        # Load IDs to get count and determine embedding shape
        with open(ids_file, 'rb') as f:
            ids = pickle.load(f)

        record_count = len(ids)
        # Assume 384-dimensional embeddings (standard for all-MiniLM-L6-v2)
        expected_shape = (record_count, 384)

        return {
            'record_count': record_count,
            'embeddings_shape': expected_shape,
            'embeddings_file': embeddings_file,
            'ids_file': ids_file,
            'embeddings_size_mb': embeddings_stat.st_size / (1024 * 1024),
            'last_modified': max(embeddings_stat.st_mtime, ids_stat.st_mtime)
        }
    except Exception as e:
        logger.error(f"Failed to get cache file info: {str(e)}")
        return None

def load_search_cache_chunked():
    # Load search data using chunked processing and disk-backed storage
    # Check for existing cache first, regenerate only if needed
    try:
        start_time = time.time()
        cache_dir = "search_cache"

        # Check if cache files exist and try to load them
        if check_cache_files_exist(cache_dir):
            logger.info("All cache files found. Loading from disk...")

            try:
                # Get cache file information
                cache_info = get_cache_file_info(cache_dir)
                if cache_info is None:
                    raise RuntimeError("Failed to get cache file information")

                # Consistent logging for IDs
                logger.info(f"Loading IDs from {cache_info['ids_file']} ({cache_info['record_count']} records)")
                with open(cache_info['ids_file'], 'rb') as f:
                    ids = pickle.load(f)

                # Consistent logging for embeddings
                logger.info(f"Loading embeddings from {cache_info['embeddings_file']} ({cache_info['embeddings_size_mb']:.1f}MB, shape={cache_info['embeddings_shape']})")
                embeddings_mmap = np.memmap(
                    cache_info['embeddings_file'],
                    dtype=np.float32,
                    mode='r',
                    shape=cache_info['embeddings_shape']
                )

                # Consistent BM25 loading (log is inside load_from_disk)
                bm25 = LMDBBackedBM25(cache_dir)
                bm25.load_from_disk()

                load_time = time.time() - start_time
                #logger.info(f"Cache loaded from disk in {load_time:.2f}s")
                #logger.info(f"Loaded {len(ids)} records with memory-mapped storage")

                force_garbage_collection()

                # Load cache metadata if available
                cache_metadata = {}
                metadata_file = f"{cache_dir}/metadata.pkl"
                if os.path.exists(metadata_file):
                    try:
                        with open(metadata_file, 'rb') as f:
                            cache_metadata = pickle.load(f)
                    except Exception as e:
                        logger.warning(f"Failed to load cache metadata: {str(e)}")

                return {
                    'ids': np.array(ids),
                    'embeddings': embeddings_mmap,  # Memory-mapped array
                    'bm25': bm25,  # Disk-backed BM25
                    'last_updated': cache_info['last_modified'],
                    'cache_dir': cache_dir,
                    'is_memory_mapped': True,
                    'loaded_from_existing_cache': True,
                    'metadata': cache_metadata
                }

            except Exception as e:
                logger.warning(f"Failed to load existing cache: {str(e)}")
                logger.info("Falling back to regenerating cache from database...")
        else:
            logger.info("Cache files not found, generating new cache from database...")

        # Generate new cache if loading existing cache failed or files don't exist
        data_info = load_data_chunked_and_split(cache_dir)

        # Load embeddings as memory-mapped array
        embeddings_mmap = np.memmap(
            data_info['embeddings_file'],
            dtype=np.float32,
            mode='r',
            shape=data_info['shape']
        )

        # Load IDs
        with open(data_info['ids_file'], 'rb') as f:
            ids = pickle.load(f)

        # BM25 is already built and ready
        bm25 = data_info['bm25']

        load_time = time.time() - start_time
        logger.info(f"Chunked cache loading completed in {load_time:.2f}s")
        logger.info(f"Loaded {len(ids)} records with memory-mapped storage")

        force_garbage_collection()

        return {
            'ids': np.array(ids),
            'embeddings': embeddings_mmap,  # Memory-mapped array
            'bm25': bm25,  # Disk-backed BM25
            'last_updated': safe_timestamp(),
            'cache_dir': cache_dir,
            'is_memory_mapped': True,
            'loaded_from_existing_cache': False
        }

    except Exception as e:
        logger.error(f"Chunked cache loading failed: {str(e)}")
        raise RuntimeError(f"Cache loading failed: {str(e)}")

def format_datetime_for_sql_server(dt):
    # Format datetime for SQL Server: YYYY-MM-DD HH:MM:SS.mmm (3-digit milliseconds)
    if dt is None:
        return None

    if isinstance(dt, str):
        # Clean up string format - just replace T with space if present
        cleaned = dt.replace('T', ' ').strip()
        return cleaned
    else:
        # Format datetime object to SQL Server format with 3-digit milliseconds
        return dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

def safe_timestamp():
    # Return a safe timestamp that won't cause overflow issues
    try:
        current_time = time.time()
        # Check if timestamp is within reasonable bounds (year 1970-2038 range)
        if current_time < 0 or current_time > 2147483647:  # 2038-01-19 03:14:07 UTC
            logger.warning(f"Timestamp {current_time} is out of safe range, using fallback")
            return 1640995200.0  # 2022-01-01 00:00:00 UTC as fallback
        return current_time
    except Exception as e:
        logger.warning(f"Error getting timestamp: {e}, using fallback")
        return 1640995200.0  # 2022-01-01 00:00:00 UTC as fallback





def extend_embeddings_mmap(cache_dir, new_embeddings):
    # Extend the memory-mapped embeddings file with new embeddings
    # This function handles Windows file locking issues by temporarily closing existing memory maps
    global search_cache
    embeddings_file = f"{cache_dir}/embeddings.dat"

    try:
        # Get current embeddings info
        cache_info = get_cache_file_info(cache_dir)
        if cache_info is None:
            raise RuntimeError("Cannot get current cache info")

        current_shape = cache_info['embeddings_shape']
        current_count = current_shape[0]
        new_count = len(new_embeddings)
        total_count = current_count + new_count

        logger.info(f"Extending embeddings from {current_count} to {total_count} records")

        # CRITICAL: Temporarily close existing memory-mapped file to avoid Windows file locking
        # Store reference to old embeddings and temporarily set to None
        old_embeddings_mmap = None
        if search_cache and 'embeddings' in search_cache:
            old_embeddings_mmap = search_cache['embeddings']
            search_cache['embeddings'] = None
            # Force deletion to close the memory map
            del old_embeddings_mmap
            force_garbage_collection()
            logger.info("Temporarily closed existing embeddings memory map to avoid file locking")

        # Create temporary file for new extended embeddings
        temp_embeddings_file = f"{embeddings_file}.tmp"

        # Create new memory-mapped file with extended size
        new_embeddings_mmap = np.memmap(
            temp_embeddings_file,
            dtype=np.float32,
            mode='w+',
            shape=(total_count, 384)
        )

        # Copy existing embeddings
        if current_count > 0:
            existing_embeddings_mmap = np.memmap(
                embeddings_file,
                dtype=np.float32,
                mode='r',
                shape=current_shape
            )
            new_embeddings_mmap[:current_count] = existing_embeddings_mmap[:]
            del existing_embeddings_mmap  # Close the old file

        # Add new embeddings
        if new_count > 0:
            new_embeddings_array = np.vstack(new_embeddings).astype(np.float32)
            new_embeddings_mmap[current_count:] = new_embeddings_array
            del new_embeddings_array

            # GC trigger after large array operation
            force_garbage_collection()

        # Flush and close new file
        new_embeddings_mmap.flush()
        del new_embeddings_mmap

        # Atomic replacement: rename temp file to actual file
        import shutil
        shutil.move(temp_embeddings_file, embeddings_file)

        logger.info(f"Embeddings file extended successfully to {total_count} records")

        return total_count

    except Exception as e:
        # Clean up temp file if it exists
        temp_file = f"{embeddings_file}.tmp"
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
            except:
                pass

        # If we temporarily closed the embeddings, try to restore it
        if search_cache and search_cache.get('embeddings') is None:
            try:
                # Attempt to restore the original memory map
                cache_info = get_cache_file_info(cache_dir)
                if cache_info:
                    restored_embeddings = np.memmap(
                        embeddings_file,
                        dtype=np.float32,
                        mode='r',
                        shape=cache_info['embeddings_shape']
                    )
                    search_cache['embeddings'] = restored_embeddings
                    logger.info("Restored original embeddings memory map after error")
            except Exception as restore_error:
                logger.error(f"Failed to restore embeddings after error: {str(restore_error)}")

        logger.error(f"Failed to extend embeddings file: {str(e)}")
        raise

def update_cache_incrementally(new_ids, new_embeddings, new_texts):
    # Update the search cache incrementally with new data
    global search_cache

    if search_cache is None:
        raise RuntimeError("Search cache not loaded")

    try:
        start_time = time.time()
        cache_dir = search_cache.get('cache_dir', 'search_cache')

        logger.info(f"Starting incremental cache update with {len(new_ids)} new records")

        # Step 1: Extend embeddings memory-mapped file
        total_count = extend_embeddings_mmap(cache_dir, new_embeddings)

        # Step 2: Update BM25 index incrementally
        search_cache['bm25'].add_documents_incremental(new_ids, new_texts)

        # Step 3: Update IDs list and save to disk
        current_ids = search_cache['ids'].tolist()
        updated_ids = current_ids + new_ids

        # Save updated IDs to disk
        ids_file = f"{cache_dir}/ids.pkl"
        temp_ids_file = f"{ids_file}.tmp"

        with open(temp_ids_file, 'wb') as f:
            pickle.dump(updated_ids, f)

        # Atomic replacement
        import shutil
        shutil.move(temp_ids_file, ids_file)

        # Step 4: Hot swap - update in-memory cache references atomically
        # Load new memory-mapped embeddings
        new_embeddings_mmap = np.memmap(
            f"{cache_dir}/embeddings.dat",
            dtype=np.float32,
            mode='r',
            shape=(total_count, 384)
        )

        # Get latest Entered_Time from new records for metadata update
        latest_entered_time = None
        if new_ids:
            try:
                sample_ids = new_ids[-min(10, len(new_ids)):]  # Get last few IDs
                placeholders = ','.join([f':id_{i}' for i in range(len(sample_ids))])
                params = {f'id_{i}': sample_ids[i] for i in range(len(sample_ids))}

                with engine.connect() as conn:
                    result = conn.execute(sa_text(f"""
                        SELECT MAX(Entered_Time) as max_entered_time
                        FROM ReportNLP
                        WHERE Id IN ({placeholders})
                    """), params)

                    row = result.fetchone()
                    latest_entered_time = row[0] if row and row[0] else None
            except Exception as e:
                logger.warning(f"Failed to get latest Entered_Time for metadata: {str(e)}")

        # Update cache metadata
        cache_metadata = search_cache.get('metadata', {})
        cache_metadata.update({
            'record_count': len(updated_ids),
            'latest_entered_time': latest_entered_time,
            'last_incremental_update': safe_timestamp()
        })

        # Save updated metadata to disk
        metadata_file = f"{cache_dir}/metadata.pkl"
        temp_metadata_file = f"{metadata_file}.tmp"

        with open(temp_metadata_file, 'wb') as f:
            pickle.dump(cache_metadata, f)

        import shutil
        shutil.move(temp_metadata_file, metadata_file)

        # Update cache structure atomically
        old_embeddings = search_cache['embeddings']
        search_cache['embeddings'] = new_embeddings_mmap
        search_cache['ids'] = np.array(updated_ids)
        search_cache['last_updated'] = safe_timestamp()
        search_cache['metadata'] = cache_metadata

        # Clean up old embeddings reference (may be None if temporarily closed)
        if old_embeddings is not None:
            del old_embeddings

        update_time = time.time() - start_time
        #logger.info(f"Incremental cache update completed in {update_time:.2f}s")
        #logger.info(f"Cache now contains {len(updated_ids)} total records")

        force_garbage_collection()

        return {
            'success': True,
            'new_records_added': len(new_ids),
            'total_records': len(updated_ids),
            'update_time_seconds': update_time
        }

    except Exception as e:
        logger.error(f"Incremental cache update failed: {str(e)}")
        raise RuntimeError(f"Cache update failed: {str(e)}")

def load_missing_records_chunked(temp_cache_table: str, chunk_size: int = cache_update_config['batch_size']):
    # Load missing records (not in cache) using chunked processing
    #logger.info(f"Loading missing records with chunk size {chunk_size}")

    try:
        # Get total count of missing records first (with TOP clause for debugging)
        with engine.connect() as conn:
            count_query = f"""
                SELECT COUNT(*) as total_count
                FROM (
                    SELECT {TOP_CLAUSE} Id
                    FROM {query_table}
                    WHERE empty_report = 0
                      AND empty_embedding = 0
                      AND extracted_text IS NOT NULL
                      AND embedding_vector IS NOT NULL
                      AND NOT EXISTS (SELECT 1 FROM {temp_cache_table} temp WHERE temp.Id = CAST({query_table}.Id AS VARCHAR(30)))
                ) subquery
            """
            result = conn.execute(sa_text(count_query))
            total_count = result.fetchone()[0]

        if total_count == 0:
            logger.info("No missing records to load")
            return [], [], []

        logger.info(f"Loading {total_count} missing records with chunk size {chunk_size}")

        new_ids = []
        new_embeddings = []
        new_texts = []
        processed = 0

        # Process in chunks using the same batch size as full cache build
        for offset in range(0, total_count, chunk_size):
            logger.info(f"Processing missing records chunk {offset//chunk_size + 1}: records {offset}-{min(offset+chunk_size, total_count)}")

            # Query missing records - handle TOP clause conflict with OFFSET
            if TOP_CLAUSE:
                # When using TOP, we can't use OFFSET, so limit the total records instead
                remaining_records = min(chunk_size, total_count - offset)
                if offset >= total_count or remaining_records <= 0:
                    break

                query = f"""
                    SELECT {TOP_CLAUSE} Id, extracted_text, embedding_vector, Entered_Time
                    FROM {query_table}
                    WHERE empty_report = 0
                      AND empty_embedding = 0
                      AND extracted_text IS NOT NULL
                      AND embedding_vector IS NOT NULL
                      AND NOT EXISTS (SELECT 1 FROM {temp_cache_table} temp WHERE temp.Id = CAST({query_table}.Id AS VARCHAR(30)))
                    ORDER BY Entered_Time
                """
            else:
                # Without TOP clause, we can use OFFSET normally
                query = f"""
                    SELECT Id, extracted_text, embedding_vector, Entered_Time
                    FROM {query_table}
                    WHERE empty_report = 0
                      AND empty_embedding = 0
                      AND extracted_text IS NOT NULL
                      AND embedding_vector IS NOT NULL
                      AND NOT EXISTS (SELECT 1 FROM {temp_cache_table} temp WHERE temp.Id = CAST({query_table}.Id AS VARCHAR(30)))
                    ORDER BY Entered_Time
                    OFFSET {offset} ROWS FETCH NEXT {chunk_size} ROWS ONLY
                """

            df_chunk = cx.read_sql(
                connection_url2,
                query,
                return_type="pandas"
            )

            if df_chunk.empty:
                break

            # Process chunk data same way as full load
            chunk_ids = []
            chunk_texts = []
            chunk_embeddings = []

            for i, row in df_chunk.iterrows():
                row_id = str(row['Id'])
                text = row['extracted_text']
                embedding_str = row['embedding_vector']

                # Process embedding
                try:
                    if embedding_str:
                        embedding = np.fromstring(embedding_str, sep=',', dtype=np.float32)
                        if len(embedding) == 384:  # Validate dimension
                            chunk_embeddings.append(embedding.astype(np.float32))
                            chunk_ids.append(row_id)
                            chunk_texts.append(text)
                except (ValueError, AttributeError) as e:
                    logger.warning(f"Failed to parse embedding for ID {row_id}: {e}")
                    continue

            # Accumulate results
            new_ids.extend(chunk_ids)
            new_texts.extend(chunk_texts)
            new_embeddings.extend(chunk_embeddings)
            processed += len(chunk_ids)

            # Force cleanup after each chunk
            del df_chunk
            force_garbage_collection()

            logger.info(f"Missing records progress: {processed}/{total_count} records processed ({100*processed/total_count:.1f}%)")

        logger.info(f"Missing records loading complete: {len(new_ids)} records loaded")

        return new_ids, new_embeddings, new_texts

    except Exception as e:
        logger.error(f"Failed to load missing records: {str(e)}")
        raise RuntimeError(f"Missing records loading failed: {str(e)}")



def perform_incremental_cache_update():
    # Comprehensive incremental cache update using database-driven ID comparison
    # This catches both new records AND reprocessed records (like ETL reprocessing)
    global search_cache

    try:
        if search_cache is None:
            logger.warning("Search cache not loaded, cannot perform incremental update")
            return {'success': False, 'reason': 'Cache not loaded'}

        logger.info("Starting incremental cache update")
        start_time = time.time()

        # Step 1: Create temp table with current cache IDs (same approach as ETL reprocessing)
        cached_ids = search_cache['ids'].tolist()
        temp_cache_table = f"{query_table}_cache_ids"

        logger.info(f"Creating temporary table with {len(cached_ids)} cached IDs...")

        # Create temp table and insert IDs in batches to avoid query size limits
        create_temp_table_sql = f"""
        DROP TABLE IF EXISTS {temp_cache_table};
        CREATE TABLE {temp_cache_table} (Id VARCHAR(30));
        CREATE INDEX IX_CacheIds ON {temp_cache_table}(Id);
        """

        # Insert IDs in batches of 1000 to avoid SQL query size limits (same as ETL)
        BATCH_INSERT_SIZE = 1000
        insert_batches = [cached_ids[i:i+BATCH_INSERT_SIZE] for i in range(0, len(cached_ids), BATCH_INSERT_SIZE)]

        for batch_num, id_batch in enumerate(insert_batches, 1):
            logger.info(f"Inserting batch {batch_num}/{len(insert_batches)} of cache IDs...")
            values_list = ','.join([f"('{id}')" for id in id_batch])
            insert_sql = f"INSERT INTO {temp_cache_table} (Id) VALUES {values_list};\n"
            create_temp_table_sql += insert_sql

        # Execute temp table creation with retry logic (same as ETL)
        MAX_RETRIES = 5
        RETRY_BACKOFF = 5
        attempt = 0
        while attempt < MAX_RETRIES:
            try:
                with engine.connect() as conn:
                    conn.execute(sa_text(create_temp_table_sql))
                    conn.commit()
                break
            except Exception as e:
                if "deadlocked" in str(e).lower() or "deadlock" in str(e).lower():
                    attempt += 1
                    wait_time = RETRY_BACKOFF * attempt
                    logger.warning(f"Deadlock detected in temp table creation. Retry {attempt}/{MAX_RETRIES} after {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise
        else:
            logger.error(f"Failed to create temp table after {MAX_RETRIES} retries due to deadlocks.")
            return {'success': False, 'reason': 'Temp table creation failed due to persistent deadlocks'}

        # Step 2: Find missing records using NOT EXISTS (database-side comparison)
        missing_count_query = f"""
        SELECT COUNT(*) as missing_count
        FROM (
            SELECT {TOP_CLAUSE} Id
            FROM {query_table}
            WHERE empty_report = 0
              AND empty_embedding = 0
              AND extracted_text IS NOT NULL
              AND embedding_vector IS NOT NULL
              AND NOT EXISTS (SELECT 1 FROM {temp_cache_table} temp WHERE temp.Id = CAST({query_table}.Id AS VARCHAR(30)))
        ) subquery
        """

        with engine.connect() as conn:
            result = conn.execute(sa_text(missing_count_query))
            missing_count = result.fetchone()[0]

        if missing_count == 0:
            logger.info("No missing records found, cache is up to date")
            # Clean up temp table
            try:
                with engine.connect() as conn:
                    conn.execute(sa_text(f"DROP TABLE IF EXISTS {temp_cache_table};"))
                    conn.commit()
            except Exception as e:
                logger.warning(f"Failed to clean up temp table: {e}")

            return {'success': True, 'reason': 'No missing records', 'missing_records': 0}

        logger.info(f"Found {missing_count} missing records to add to cache")

        # Step 3: Load missing records using chunked approach
        batch_size = cache_update_config['batch_size']
        new_ids, new_embeddings, new_texts = load_missing_records_chunked(temp_cache_table, batch_size)

        if len(new_ids) == 0:
            logger.info("No valid missing records found after processing")
            # Clean up temp table
            try:
                with engine.connect() as conn:
                    conn.execute(sa_text(f"DROP TABLE IF EXISTS {temp_cache_table};"))
                    conn.commit()
            except Exception as e:
                logger.warning(f"Failed to clean up temp table: {e}")

            return {'success': True, 'reason': 'No valid missing records', 'missing_records': 0}

        # Step 4: Check memory constraints
        max_memory_growth = cache_update_config.get('max_memory_growth_mb', 10240)  # Increased default for comprehensive updates
        estimated_memory_mb = len(new_embeddings) * 384 * 4 / (1024 * 1024)  # float32 = 4 bytes

        if estimated_memory_mb > max_memory_growth:
            logger.warning(f"Missing records would use {estimated_memory_mb:.1f}MB, exceeding limit of {max_memory_growth}MB")
            # Clean up temp table
            try:
                with engine.connect() as conn:
                    conn.execute(sa_text(f"DROP TABLE IF EXISTS {temp_cache_table};"))
                    conn.commit()
            except Exception as e:
                logger.warning(f"Failed to clean up temp table: {e}")

            return {
                'success': False,
                'reason': 'Memory limit exceeded',
                'estimated_memory_mb': estimated_memory_mb,
                'limit_mb': max_memory_growth
            }

        # Step 5: Perform incremental update
        update_result = update_cache_incrementally(new_ids, new_embeddings, new_texts)

        # Step 6: Clean up temp table
        try:
            with engine.connect() as conn:
                conn.execute(sa_text(f"DROP TABLE IF EXISTS {temp_cache_table};"))
                conn.commit()
            logger.info(f"Cleaned up temporary table: {temp_cache_table}")
        except Exception as e:
            logger.warning(f"Failed to clean up temp table {temp_cache_table}: {e}")

        total_time = time.time() - start_time
        logger.info(f"Incremental cache update completed successfully in {total_time:.2f}s. Added {update_result['new_records_added']} records. Total cache: {update_result['total_records']} records")

        return {
            'success': True,
            'missing_records_added': update_result['new_records_added'],
            'total_records': update_result['total_records'],
            'update_time_seconds': update_result['update_time_seconds'],
            'total_process_time_seconds': total_time,
            'memory_used_mb': estimated_memory_mb,
            'method': 'comprehensive_id_comparison'
        }

    except Exception as e:
        # Clean up temp table on error
        try:
            temp_cache_table = f"{query_table}_cache_ids"
            with engine.connect() as conn:
                conn.execute(sa_text(f"DROP TABLE IF EXISTS {temp_cache_table};"))
                conn.commit()
        except:
            pass  # Ignore cleanup errors

        logger.error(f"Incremental cache update failed: {str(e)}")
        return {
            'success': False,
            'reason': 'Comprehensive update failed',
            'error': str(e)
        }

def calculate_lexical_scores_bm25(bm25: LMDBBackedBM25, query: str) -> np.ndarray:
    # Calculate BM25 lexical similarity scores for all texts, normalized to 0-1 range
    # Track memory usage for GC triggering
    memory_before = get_memory_usage()

    raw_scores = bm25.get_scores(query)

    # Normalize to 0-1 range to match semantic score scale
    if len(raw_scores) > 0:
        max_score = np.max(raw_scores)
        if max_score > 0:
            normalized_scores = raw_scores / max_score
        else:
            normalized_scores = raw_scores
    else:
        normalized_scores = raw_scores

    # Trigger GC if memory increased significantly during query processing
    memory_after = get_memory_usage()
    memory_growth = memory_after - memory_before
    if memory_growth > 50:  # 50MB threshold
        freed = force_garbage_collection()
        logger.debug(f"Post-BM25-query GC freed {freed:.1f}MB (growth was {memory_growth:.1f}MB)")

    return normalized_scores

def fetch_full_records_by_ids(target_ids: List[str], scores: List[float]) -> List[Dict]:
    # Fetch complete records using ID list
    if not target_ids:
        return []

    try:
        # Create parameterized query for multiple IDs
        placeholders = ','.join([f':id_{i}' for i in range(len(target_ids))])
        params = {f'id_{i}': target_ids[i] for i in range(len(target_ids))}

        with engine.connect() as conn:
            result = conn.execute(sa_text(f"""
                SELECT * FROM ReportNLP 
                WHERE Id IN ({placeholders})
            """), params)
            
            # Create lookup for scores by ID
            score_lookup = {target_ids[i]: scores[i] for i in range(len(target_ids))}
            
            records = []
            columns = result.keys()
            for row in result:
                record_dict = dict(zip(columns, row))
                record_dict['hybrid_score'] = score_lookup.get(str(row.Id), 0.0)
                records.append(record_dict)
            
            # Sort by score (since SQL IN doesn't preserve order)
            records.sort(key=lambda x: x['hybrid_score'], reverse=True)
            
        return records
        
    except Exception as e:
        logger.error(f"Failed to fetch records by IDs: {str(e)}")
        raise RuntimeError(f"Record retrieval failed: {str(e)}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup and shutdown events
    global model, engine, search_cache

    # Startup
    logger.info("Starting up Search API...")
    try:
        # Load embedding model
        logger.info("Loading SentenceTransformer model...")
        model = SentenceTransformer('all-MiniLM-L6-v2', cache_folder='search_cache')
        logger.info("Model loaded successfully")

        # Initialize database connection
        logger.info("Initializing database connection...")

        # Login to DB
        engine = create_engine(
            connection_url2,
            pool_pre_ping=True,
            pool_recycle=300,
            echo=False
        )

        # Test database connection
        with engine.connect() as conn:
            conn.execute(sa_text("SELECT 1"))
        logger.info("Database connection established")

        # Load search cache with chunked processing
        logger.info("Loading search cache on disk...")
        search_cache = load_search_cache_chunked()
        logger.info(f"Search cache loaded: {len(search_cache['ids'])} records")
        #logger.info(f"Memory-mapped storage: {search_cache.get('is_memory_mapped', False)}")

        # Start system monitoring
        logger.info("Starting system monitor...")
        start_system_monitor()

        # Start cache update monitoring
        logger.info("Starting cache update monitor...")
        start_cache_update_monitor()

    except Exception as e:
        logger.error(f"Startup failed: {str(e)}")
        raise

    yield

    # Shutdown
    logger.info("Shutting down Search API...")

    # Stop monitoring threads
    stop_cache_update_monitor()
    stop_system_monitor()

    if engine:
        engine.dispose()
        logger.info("Database connection closed")

# Create FastAPI app
app = FastAPI(
    title="Hybrid Search API",
    description="API for semantic and lexical search using embeddings",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response Models
class SearchRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    table_name: Optional[str] = Field(query_table, description="Target table name")
    text_column: Optional[str] = Field("extracted_text", description="Text column to search")
    embedding_column: Optional[str] = Field("embedding_vector", description="Embedding column name")
    top_k: Optional[int] = Field(10, ge=1, le=100, description="Number of results to return")
    semantic_weight: Optional[float] = Field(0.6, ge=0.0, le=1.0, description="Weight for semantic search")
    lexical_weight: Optional[float] = Field(0.4, ge=0.0, le=1.0, description="Weight for lexical search")
    min_similarity: Optional[float] = Field(0.1, ge=0.0, le=1.0, description="Minimum similarity threshold")
    include_scores: Optional[bool] = Field(True, description="Include similarity scores in response")
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        if not v.strip():
            raise ValueError('Query cannot be empty or whitespace only')
        return v.strip()
    
    @model_validator(mode='after')
    def validate_weights(self):
        if self.semantic_weight + self.lexical_weight == 0:
            raise ValueError('At least one weight must be greater than 0')
        return self

class SearchResult(BaseModel):
    Id: str
    Type: Optional[str]
    Niche_Report_ID: str
    Entered_Time: Optional[str]
    Report_Time: Optional[str]
    Remarks: Optional[str]
    Niche_Author_ID: Optional[str]
    Niche_Enter_ID: Optional[str]
    Niche_Occurrence_ID: Optional[str]
    Occurrence_Number: Optional[str]
    Occurrence_Type: Optional[str]
    Zone: Optional[str]
    Team: Optional[str]
    Municipality: Optional[str]
    AccessControlList: Optional[str]
    fixed_type: Optional[str]
    real_type: Optional[str]
    category: Optional[str]
    gzip: Optional[int]
    empty_report: Optional[int]
    empty_embedding: Optional[int]
    ETL_Proc_Time: Optional[str]
    extracted_text: Optional[str]
    semantic_score: Optional[float] = None
    lexical_score: Optional[float] = None
    hybrid_score: Optional[float] = None
    text_length: Optional[int] = None

class SearchResponse(BaseModel):
    success: bool
    query: str
    total_results: int
    execution_time_ms: float
    search_parameters: Dict[str, Any]
    results: List[SearchResult]

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    error_type: str
    timestamp: str
    request_id: Optional[str] = None

# Middleware for request logging and memory tracking
@app.middleware("http")
async def memory_tracking_middleware(request: Request, call_next):
    # Track memory usage per request
    start_time = time.time()
    start_memory = get_memory_usage()
    request_id = f"{int(time.time())}-{id(request)}"
    
    # Log request
    logger.info(f"Request {request_id}: {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        
        end_memory = get_memory_usage()
        process_time = (time.time() - start_time) * 1000
        memory_diff = end_memory - start_memory
        
        # Log if memory increased significantly
        if memory_diff > 5:  # More than 5MB increase
            logger.warning(f"Request {request_id} increased memory by {memory_diff:.1f}MB "
                         f"(from {start_memory:.1f}MB to {end_memory:.1f}MB)")
        
        # Force GC every 50 requests or if memory increased too much
        if not hasattr(request.state, 'request_count'):
            request.state.request_count = 0
        request.state.request_count += 1
            
        if request.state.request_count % 25 == 0 or memory_diff > 5:  # More aggressive: every 25 requests or 5MB growth
            freed = force_garbage_collection()
            if freed > 1:
                logger.info(f"Garbage collection freed {freed:.1f}MB after request {request_id}")
        
        logger.info(f"Request {request_id} completed in {process_time:.2f}ms with status {response.status_code}")
        return response
        
    except Exception as e:
        # Always run GC on exceptions
        force_garbage_collection()
        process_time = (time.time() - start_time) * 1000
        logger.error(f"Request {request_id} failed after {process_time:.2f}ms: {str(e)}")
        raise

# Custom exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            error_type="HTTPException",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            request_id=str(id(request))
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    error_id = str(id(request))
    logger.error(f"Unexpected error {error_id}: {str(exc)}\n{traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error. Please check logs for details.",
            error_type="InternalServerError",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            request_id=error_id
        ).dict()
    )

# Helper functions
def validate_table_column_names(table_name: str, text_column: str, embedding_column: str):
    # Validate table and column names to prevent SQL injection
    # Very permissive pattern that allows most SQL Server naming conventions
    # but blocks obvious SQL injection attempts
    def is_valid_sql_identifier(name: str) -> bool:
        # Allow:
        # - Regular identifiers: table, [table], schema.table, [schema].[table]
        # - Temp tables: #table, ##table
        # - 4-part names: server.database.schema.table
        # - Quoted identifiers with spaces: [table name]
        
        # Block obvious injection attempts
        dangerous_patterns = [
            r';\s*drop\s+',
            r';\s*delete\s+',
            r';\s*insert\s+',
            r';\s*update\s+',
            r';\s*exec\s*\(',
            r';\s*execute\s*\(',
            r'--',
            r'/\*',
            r'\*/',
            r'union\s+select',
            r'or\s+1\s*=\s*1',
            r'and\s+1\s*=\s*1'
        ]
        
        name_lower = name.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, name_lower):
                return False
        
        # Allow most valid SQL Server identifiers
        # This pattern is very permissive but safe
        valid_pattern = r'^[a-zA-Z0-9_#\[\].\s-]+$'
        return re.match(valid_pattern, name) is not None
    
    if not is_valid_sql_identifier(table_name):
        raise ValueError(f"Invalid or potentially unsafe table name: {table_name}")
    if not is_valid_sql_identifier(text_column):
        raise ValueError(f"Invalid or potentially unsafe text column name: {text_column}")
    if not is_valid_sql_identifier(embedding_column):
        raise ValueError(f"Invalid or potentially unsafe embedding column name: {embedding_column}")

def generate_query_embedding(query: str) -> str:
    # Generate embedding for search query
    try:
        if model is None:
            raise RuntimeError("Embedding model not loaded")
        
        logger.debug(f"Generating embedding for query: {query[:50]}...")
        embedding = model.encode([query], convert_to_tensor=False, show_progress_bar=False)[0]
        embedding_str = ','.join(map(str, embedding))
        logger.debug(f"Generated embedding with {len(embedding)} dimensions")
        return embedding_str
    except Exception as e:
        logger.error(f"Failed to generate embedding: {str(e)}")
        raise RuntimeError(f"Embedding generation failed: {str(e)}")

def execute_search_query(params: dict) -> List[Dict]:
    # Execute the hybrid search SQL query
    try:
        if engine is None:
            raise RuntimeError("Database connection not available")
        
        with engine.connect() as conn:
            logger.debug(f"Executing search on table: {params['table_name']}")
            
            result = conn.execute(
                sa_text("""
                    EXEC HybridSearchDynamic
                        @table_name = :table_name,
                        @text_column = :text_column,
                        @embedding_column = :embedding_column,
                        @query = :query,
                        @query_embedding = :query_embedding,
                        @top_k = :top_k,
                        @semantic_weight = :semantic_weight,
                        @lexical_weight = :lexical_weight,
                        @min_similarity = :min_similarity
                """),
                params
            )
            
            # Convert to list of dictionaries
            columns = result.keys()
            rows = result.fetchall()
            results = [dict(zip(columns, row)) for row in rows]
            
            logger.debug(f"Query returned {len(results)} results")
            return results
            
    except exc.SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        raise RuntimeError(f"Database query failed: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in query execution: {str(e)}")
        raise

async def optimized_hybrid_search(request: SearchRequest) -> SearchResponse:
    # Memory-optimized hybrid search using cached data + ID lookup
    start_time = time.time()
    start_memory = get_memory_usage()
    
    try:
        # Check if cache is available
        if search_cache is None:
            raise RuntimeError("Search cache not loaded")
        
        logger.info(f"Processing search: '{request.query}' (Memory: {start_memory:.1f}MB)")
        
        # Step 1: Generate query embedding
        query_embedding = model.encode([request.query], convert_to_tensor=False, show_progress_bar=False)[0]
        query_embedding = query_embedding.astype(np.float32)
        
        # Step 2: Calculate semantic similarities (batched for memory-mapped arrays)
        embeddings = search_cache['embeddings']
        total_docs = len(embeddings)
        semantic_scores = np.zeros(total_docs, dtype=np.float32)

        # Process in batches to control memory usage with memory-mapped arrays
        search_batch_size = 100000
        for i in range(0, total_docs, search_batch_size):
            end_idx = min(i + search_batch_size, total_docs)

            # Load batch from memory-mapped array and convert to float32
            batch_embeddings = embeddings[i:end_idx].astype(np.float32)

            # Calculate cosine similarity for this batch
            batch_scores = np.dot(batch_embeddings, query_embedding) / (
                np.linalg.norm(batch_embeddings, axis=1) * np.linalg.norm(query_embedding)
            )
            semantic_scores[i:end_idx] = batch_scores

            # Clean up batch and trigger GC if needed
            del batch_embeddings

            # GC trigger after each semantic similarity batch
            if i % 5 == 0:  # Every 5 batches
                current_memory = get_memory_usage()
                if current_memory > start_memory + 100:  # 100MB threshold for batched operations
                    freed = force_garbage_collection()
                    logger.debug(f"Post-semantic-batch GC freed {freed:.1f}MB")
        
        # Step 3: Calculate lexical scores using BM25
        lexical_scores = calculate_lexical_scores_bm25(search_cache['bm25'], request.query)

        # Additional GC trigger after lexical scoring (as per LMDB workflow)
        current_memory = get_memory_usage()
        if current_memory > start_memory + 50:  # 50MB threshold
            freed = force_garbage_collection()
            logger.debug(f"Post-lexical-scoring GC freed {freed:.1f}MB")

        # Step 4: Combine scores (reuse arrays)
        hybrid_scores = (
            request.semantic_weight * semantic_scores + 
            request.lexical_weight * lexical_scores
        )
        
        # Step 5: Filter by minimum similarity and get top K
        valid_indices = np.where(hybrid_scores >= request.min_similarity)[0]
        
        if len(valid_indices) == 0:
            # Clean up before returning
            del semantic_scores, lexical_scores, hybrid_scores
            force_garbage_collection()
            
            logger.info("No results met minimum similarity threshold")
            return SearchResponse(
                success=True,
                query=request.query,
                total_results=0,
                execution_time_ms=(time.time() - start_time) * 1000,
                results=[],
                search_parameters={
                    'table_name': request.table_name,
                    'semantic_weight': request.semantic_weight,
                    'lexical_weight': request.lexical_weight,
                    'top_k': request.top_k,
                    'min_similarity': request.min_similarity
                }
            )
        
        # Get top K from valid results
        valid_scores = hybrid_scores[valid_indices]
        top_k_indices_in_valid = np.argsort(valid_scores)[-request.top_k:][::-1]
        top_indices = valid_indices[top_k_indices_in_valid]
        
        # Get target IDs and scores
        target_ids = search_cache['ids'][top_indices].tolist()
        target_scores = hybrid_scores[top_indices].tolist()
        # Store individual scores for response
        target_semantic_scores = semantic_scores[top_indices].tolist()
        target_lexical_scores = lexical_scores[top_indices].tolist()
        
        # Clean up large arrays and trigger GC
        del semantic_scores, lexical_scores, hybrid_scores, valid_indices, valid_scores

        # GC trigger after large array cleanup
        current_memory = get_memory_usage()
        if current_memory > start_memory + 50:  # 50MB threshold
            freed = force_garbage_collection()
            logger.debug(f"Post-array-cleanup GC freed {freed:.1f}MB")
        
        search_time = time.time() - start_time
        
        # Step 6: Fetch full records from database
        full_records_data = fetch_full_records_by_ids(target_ids, target_scores)

        # GC trigger after database record fetching
        current_memory = get_memory_usage()
        if current_memory > start_memory + 50:  # 50MB threshold
            freed = force_garbage_collection()
            logger.debug(f"Post-db-fetch GC freed {freed:.1f}MB")
        
        # Create lookup dictionaries for individual scores
        semantic_lookup = {target_ids[i]: target_semantic_scores[i] for i in range(len(target_ids))}
        lexical_lookup = {target_ids[i]: target_lexical_scores[i] for i in range(len(target_ids))}
        
        # Process results into SearchResult objects
        processed_results = []
        for row in full_records_data:
            record_id = str(row.get('Id', ''))
            result = SearchResult(
                Id=record_id,
                Type=row.get('Type'),
                Niche_Report_ID=str(row.get('Niche_Report_ID', '')),
                Entered_Time=str(row.get('Entered_Time', '')) if row.get('Entered_Time') else None,
                Report_Time=str(row.get('Report_Time', '')) if row.get('Report_Time') else None,
                Remarks=row.get('Remarks'),
                Niche_Author_ID=row.get('Niche_Author_ID'),
                Niche_Enter_ID=row.get('Niche_Enter_ID'),
                Niche_Occurrence_ID=row.get('Niche_Occurrence_ID'),
                Occurrence_Number=row.get('Occurrence_Number'),
                Occurrence_Type=row.get('Occurrence_Type'),
                Zone=row.get('Zone'),
                Team=row.get('Team'),
                Municipality=row.get('Municipality'),
                AccessControlList=row.get('AccessControlList'),
                fixed_type=row.get('fixed_type'),
                real_type=row.get('real_type'),
                category=row.get('category'),
                gzip=row.get('gzip'),
                empty_report=row.get('empty_report'),
                empty_embedding=row.get('empty_embedding'),
                ETL_Proc_Time=row.get('ETL_Proc_Time'),
                extracted_text=row.get('extracted_text'),
                semantic_score=float(semantic_lookup.get(record_id, 0)) if request.include_scores else None,
                lexical_score=float(lexical_lookup.get(record_id, 0)) if request.include_scores else None,
                hybrid_score=float(row.get('hybrid_score', 0)) if request.include_scores else None,
                text_length=len(row.get('extracted_text', '')) if request.include_scores else None
            )
            processed_results.append(result)
        
        total_time = (time.time() - start_time) * 1000
        end_memory = get_memory_usage()
        memory_used = end_memory - start_memory
        
        logger.info(f"Search completed: {len(processed_results)} results in {total_time:.2f}ms "
                   f"(Memory: {start_memory:.1f}MB -> {end_memory:.1f}MB, diff: {memory_used:+.1f}MB)")
        
        # Force cleanup if we used too much memory
        if memory_used > 5:
            force_garbage_collection()

        # Final GC trigger to ensure complete cleanup after search
        final_memory = get_memory_usage()
        if final_memory > start_memory + 10:  # 10MB threshold for final cleanup
            freed = force_garbage_collection()
            logger.debug(f"Final search GC freed {freed:.1f}MB")

        return SearchResponse(
            success=True,
            query=request.query,
            total_results=len(processed_results),
            execution_time_ms=total_time,
            results=processed_results,
            search_parameters={
                'table_name': request.table_name,
                'semantic_weight': request.semantic_weight,
                'lexical_weight': request.lexical_weight,
                'top_k': request.top_k,
                'min_similarity': request.min_similarity
            }
        )
        
    except Exception as e:
        # Always clean up on error
        force_garbage_collection()
        logger.error(f"Optimized search failed: {str(e)}")
        raise RuntimeError(f"Search execution failed: {str(e)}")

@app.get("/memory")
async def memory_status():
    # Get current memory usage statistics
    try:
        current_mem = get_memory_usage()
        cache_size = len(search_cache['ids']) if search_cache else 0
        
        # Get process info
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            "memory_mb": round(current_mem, 1),
            "memory_gb": round(current_mem / 1024, 2),
            "cache_records": cache_size,
            "memory_per_record_kb": round((current_mem * 1024) / cache_size, 2) if cache_size > 0 else 0,
            "virtual_memory_mb": round(memory_info.vms / 1024 / 1024, 1),
            "peak_memory_mb": round(memory_info.peak_wset / 1024 / 1024, 1) if hasattr(memory_info, 'peak_wset') else None,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "process_id": os.getpid()
        }
    except Exception as e:
        logger.error(f"Memory check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Memory check failed")

@app.post("/gc")
async def manual_garbage_collection():
    # Manually trigger garbage collection
    try:
        before = get_memory_usage()

        # Get GC stats before
        gc_stats_before = gc.get_stats()
        gc_count_before = gc.get_count()

        freed = force_garbage_collection()
        after = get_memory_usage()

        # Get GC stats after
        gc_stats_after = gc.get_stats()
        gc_count_after = gc.get_count()

        return {
            "success": True,
            "memory_before_mb": round(before, 1),
            "memory_after_mb": round(after, 1),
            "memory_freed_mb": round(freed, 1),
            "gc_collections_before": gc_count_before,
            "gc_collections_after": gc_count_after,
            "gc_stats": {
                "generation_0_collections": gc_stats_after[0]['collections'] - gc_stats_before[0]['collections'],
                "generation_1_collections": gc_stats_after[1]['collections'] - gc_stats_before[1]['collections'],
                "generation_2_collections": gc_stats_after[2]['collections'] - gc_stats_before[2]['collections']
            },
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
    except Exception as e:
        logger.error(f"Manual GC failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Garbage collection failed")

@app.get("/monitor")
async def monitor_status():
    # Get system monitor status
    global monitor_running, monitor_thread

    try:
        is_running = monitor_running and monitor_thread and monitor_thread.is_alive()

        return {
            "monitor_running": is_running,
            "monitor_thread_alive": monitor_thread.is_alive() if monitor_thread else False,
            "pid": os.getpid(),
            "current_memory_mb": get_memory_usage(),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
    except Exception as e:
        logger.error(f"Monitor status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Monitor status check failed")

@app.post("/monitor/start")
async def start_monitor():
    # Start the system monitor
    try:
        start_system_monitor()
        return {
            "success": True,
            "message": "System monitor started",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
    except Exception as e:
        logger.error(f"Failed to start monitor: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to start monitor")

@app.post("/monitor/stop")
async def stop_monitor():
    # Stop the system monitor
    try:
        stop_system_monitor()
        return {
            "success": True,
            "message": "System monitor stopped",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
    except Exception as e:
        logger.error(f"Failed to stop monitor: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to stop monitor")

# API Endpoints
@app.post("/search", response_model=SearchResponse)
async def hybrid_search(request: SearchRequest):
    # Perform hybrid semantic and lexical search
    try:
        logger.info(f"Processing search request: '{request.query}' on table '{request.table_name}'")
        
        # Use optimized search instead of stored procedure
        return await optimized_hybrid_search(request)
        
    except ValueError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except RuntimeError as e:
        logger.error(f"Runtime error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

@app.get("/search")
async def simple_search(
        q: str,
        table: str = query_table,
        text_column: str = "extracted_text",
        embedding_column: str = "embedding_vector",
        top_k: int = 5,
        semantic_weight: float = 0.8,
        lexical_weight: float = 0.2,
        min_similarity: float = 0.1,
        include_scores: bool = True
    ):
    # Simple GET endpoint for basic searches with all parameters
    search_request = SearchRequest(
        query=q,
        table_name=table,
        text_column=text_column,
        embedding_column=embedding_column,
        top_k=top_k,
        semantic_weight=semantic_weight,
        lexical_weight=lexical_weight,
        min_similarity=min_similarity,
        include_scores=include_scores
    )
    return await hybrid_search(search_request)

@app.get("/health")
async def health_check():
    # Health check endpoint
    try:
        # Check model
        model_status = model is not None
        
        # Check database
        db_status = False
        if engine:
            try:
                with engine.connect() as conn:
                    conn.execute(sa_text("SELECT 1"))
                db_status = True
            except Exception:
                pass
        
        # Check cache
        cache_status = search_cache is not None
        cache_size = len(search_cache['ids']) if cache_status else 0
        cache_age = time.time() - search_cache['last_updated'] if cache_status else None
        cache_from_disk = search_cache.get('loaded_from_existing_cache', False) if cache_status else False

        status = "healthy" if (model_status and db_status and cache_status) else "unhealthy"

        return {
            "status": status,
            "model_loaded": model_status,
            "database_connected": db_status,
            "cache_loaded": cache_status,
            "cache_size": cache_size,
            "cache_age_seconds": cache_age,
            "cache_loaded_from_disk": cache_from_disk,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Health check failed")

@app.get("/cache-status")
async def cache_status():
    # Get detailed cache status information
    try:
        if search_cache is None:
            return {
                "cache_loaded": False,
                "message": "No cache loaded"
            }

        cache_dir = search_cache.get('cache_dir', 'search_cache')
        cache_files_exist = check_cache_files_exist(cache_dir)

        return {
            "cache_loaded": True,
            "cache_size": len(search_cache['ids']),
            "loaded_from_existing_cache": search_cache.get('loaded_from_existing_cache', False),
            "cache_age_seconds": time.time() - search_cache['last_updated'],
            "is_memory_mapped": search_cache.get('is_memory_mapped', False),
            "cache_directory": cache_dir,
            "cache_files_exist": cache_files_exist,
            "last_updated": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(search_cache['last_updated'])),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"Cache status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Cache status check failed")

@app.post("/refresh-cache")
async def refresh_cache(force_regenerate: bool = False):
    # Manually refresh the search cache
    # If force_regenerate=True, will regenerate from database even if cache files exist
    global search_cache

    try:
        logger.info(f"Manual cache refresh requested (force_regenerate={force_regenerate})")
        old_size = len(search_cache['ids']) if search_cache else 0
        old_from_disk = search_cache.get('loaded_from_existing_cache', False) if search_cache else False

        if force_regenerate:
            # Temporarily remove cache files to force regeneration
            cache_dir = "search_cache"
            cache_files = [
                f"{cache_dir}/embeddings.dat",
                f"{cache_dir}/ids.pkl",
                f"{cache_dir}/bm25_vocab.pkl",
                f"{cache_dir}/bm25_doc_lengths.pkl",
                f"{cache_dir}/bm25_term_doc_freq.pkl"
            ]

            backup_files = []
            for file_path in cache_files:
                if os.path.exists(file_path):
                    backup_path = f"{file_path}.backup"
                    os.rename(file_path, backup_path)
                    backup_files.append((file_path, backup_path))

            try:
                search_cache = load_search_cache_chunked()
                # Remove backup files if successful
                for _, backup_path in backup_files:
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
            except Exception as e:
                # Restore backup files if regeneration failed
                for file_path, backup_path in backup_files:
                    if os.path.exists(backup_path):
                        os.rename(backup_path, file_path)
                raise e
        else:
            search_cache = load_search_cache_chunked()

        new_size = len(search_cache['ids'])
        new_from_disk = search_cache.get('loaded_from_existing_cache', False)

        logger.info(f"Cache refreshed: {old_size} -> {new_size} records")

        return {
            "success": True,
            "message": f"Cache refreshed successfully",
            "old_size": old_size,
            "new_size": new_size,
            "old_loaded_from_disk": old_from_disk,
            "new_loaded_from_disk": new_from_disk,
            "force_regenerate": force_regenerate,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"Cache refresh failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Cache refresh failed: {str(e)}")

@app.post("/cache/update-incremental")
async def trigger_incremental_cache_update():
    # Manually trigger incremental cache update
    try:
        logger.info("Manual incremental cache update requested")

        update_result = perform_incremental_cache_update()

        if update_result['success']:
            return {
                "success": True,
                "message": "Incremental cache update completed",
                "result": update_result,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
        else:
            return {
                "success": False,
                "message": "Incremental cache update failed or not needed",
                "result": update_result,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

    except Exception as e:
        logger.error(f"Manual incremental cache update failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Incremental cache update failed: {str(e)}")

@app.get("/cache/check-updates")
async def check_cache_updates():
    # Check cache update configuration and status
    try:
        if search_cache is None:
            raise HTTPException(status_code=503, detail="Search cache not loaded")

        cache_size = len(search_cache['ids']) if search_cache else 0

        return {
            "cache_records": cache_size,
            "update_method": "comprehensive_id_comparison",
            "auto_update_enabled": cache_update_config.get('auto_update_enabled', True),
            "check_interval_seconds": cache_update_config.get('check_interval_seconds', 300),
            "last_updated": search_cache.get('last_updated', None),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "note": "Use /cache/update to trigger comprehensive update check"
        }

    except Exception as e:
        logger.error(f"Cache update check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Cache update check failed: {str(e)}")

@app.get("/cache/update-config")
async def get_cache_update_config():
    # Get current cache update configuration
    return {
        "config": cache_update_config,
        "monitor_running": cache_update_running,
        "monitor_thread_alive": cache_update_thread.is_alive() if cache_update_thread else False,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }

@app.post("/cache/update-config")
async def update_cache_update_config(
    auto_update_enabled: Optional[bool] = None,
    check_interval_seconds: Optional[int] = None,
    batch_size: Optional[int] = None,
    max_memory_growth_mb: Optional[int] = None
):
    # Update cache update configuration
    global cache_update_config

    try:
        old_config = cache_update_config.copy()

        if auto_update_enabled is not None:
            cache_update_config['auto_update_enabled'] = auto_update_enabled
        if check_interval_seconds is not None:
            cache_update_config['check_interval_seconds'] = max(60, check_interval_seconds)  # Minimum 1 minute
        if batch_size is not None:
            cache_update_config['batch_size'] = max(1000, batch_size)  # Minimum 1000
        if max_memory_growth_mb is not None:
            cache_update_config['max_memory_growth_mb'] = max(10, max_memory_growth_mb)  # Minimum 10MB

        # Restart monitor if auto_update_enabled changed
        if old_config.get('auto_update_enabled') != cache_update_config.get('auto_update_enabled'):
            if cache_update_config.get('auto_update_enabled'):
                start_cache_update_monitor()
            else:
                stop_cache_update_monitor()

        logger.info(f"Cache update configuration updated: {cache_update_config}")

        return {
            "success": True,
            "message": "Cache update configuration updated",
            "old_config": old_config,
            "new_config": cache_update_config,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"Failed to update cache configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Configuration update failed: {str(e)}")

@app.get("/")
async def root():
    # Root endpoint with API information
    return {
        "message": "Hybrid Search API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "endpoints": {
            "POST /search": "Hybrid search via POST request with JSON body",
            "GET /search": "Hybrid search via GET request",
            "GET /cache-status": "Check detailed cache status and loading information",
            "POST /refresh-cache": "Manually refresh the search cache (supports force_regenerate parameter)",
            "GET /memory": "Check current memory usage statistics",
            "POST /gc": "Manually trigger garbage collection",
            "GET /monitor": "Check system monitor status",
            "POST /monitor/start": "Start the real-time system monitor",
            "POST /monitor/stop": "Stop the real-time system monitor",
        },
        "performance": {
            "approach": "Chunked loading + memory-mapped embeddings + disk-backed BM25",
            "memory_optimization": "Memory-mapped float32 embeddings, disk-backed BM25, chunked processing",
            "data_loading": "ConnectorX chunked loading with minimal RAM usage",
            "cache_persistence": "Automatically uses existing cache files on startup, regenerates only when needed",
            "monitoring": "Real-time system monitor with persistent bottom status bar",
            "scalability": "Supports datasets much larger than available RAM"
        }
    }

# Run the application
if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True
    )