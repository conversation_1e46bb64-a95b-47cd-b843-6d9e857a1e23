#!/usr/bin/env python3
"""
Test script for OpenSearch migration validation
Tests the migrated backend_api3.py with OpenSearch integration
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_QUERIES = [
    "theft report downtown",
    "vehicle accident",
    "suspicious activity",
    "noise complaint",
    "traffic violation"
]

def test_api_health() -> bool:
    """Test basic API health"""
    try:
        response = requests.get(f"{API_BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Health: {data.get('message', 'Unknown')}")
            print(f"   Version: {data.get('version', 'Unknown')}")
            print(f"   Search Engine: {data.get('search_engine', 'Unknown')}")
            return True
        else:
            print(f"❌ API Health failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API Health error: {str(e)}")
        return False

def test_opensearch_health() -> bool:
    """Test OpenSearch cluster health"""
    try:
        response = requests.get(f"{API_BASE_URL}/opensearch/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OpenSearch Health: {data.get('cluster_status', 'Unknown')}")
            print(f"   Cluster: {data.get('cluster_name', 'Unknown')}")
            print(f"   Nodes: {data.get('number_of_nodes', 0)}")
            print(f"   Index: {data.get('index_name', 'Unknown')} ({data.get('document_count', 0):,} docs)")
            return True
        else:
            print(f"❌ OpenSearch Health failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ OpenSearch Health error: {str(e)}")
        return False

def test_search_functionality() -> bool:
    """Test search functionality with various queries"""
    print("\n🔍 Testing Search Functionality:")
    
    all_passed = True
    for i, query in enumerate(TEST_QUERIES, 1):
        try:
            # Test POST search
            search_request = {
                "query": query,
                "top_k": 5,
                "semantic_weight": 0.7,
                "lexical_weight": 0.3,
                "min_similarity": 0.1,
                "include_scores": True
            }
            
            start_time = time.time()
            response = requests.post(
                f"{API_BASE_URL}/search",
                json=search_request,
                timeout=30
            )
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                result_count = data.get('total_results', 0)
                execution_time = data.get('execution_time_ms', 0)
                
                print(f"   {i}. '{query}': ✅ {result_count} results in {execution_time:.1f}ms (API: {response_time:.1f}ms)")
                
                # Validate response structure
                if 'results' in data and 'search_parameters' in data:
                    if result_count > 0:
                        first_result = data['results'][0]
                        if 'Id' in first_result and 'extracted_text' in first_result:
                            print(f"      Sample result ID: {first_result.get('Id', 'N/A')}")
                        else:
                            print(f"      ⚠️  Result structure incomplete")
                else:
                    print(f"      ⚠️  Response structure incomplete")
                    all_passed = False
            else:
                print(f"   {i}. '{query}': ❌ Failed ({response.status_code})")
                print(f"      Error: {response.text}")
                all_passed = False
                
        except Exception as e:
            print(f"   {i}. '{query}': ❌ Error - {str(e)}")
            all_passed = False
    
    return all_passed

def test_memory_management() -> bool:
    """Test memory management and resource cleanup"""
    try:
        # Get initial memory
        response = requests.get(f"{API_BASE_URL}/memory")
        if response.status_code == 200:
            initial_memory = response.json().get('memory_mb', 0)
            print(f"✅ Memory Management: Initial memory {initial_memory:.1f}MB")
            
            # Trigger garbage collection
            gc_response = requests.post(f"{API_BASE_URL}/gc")
            if gc_response.status_code == 200:
                gc_data = gc_response.json()
                print(f"   GC freed: {gc_data.get('freed_mb', 0):.1f}MB")
            
            return True
        else:
            print(f"❌ Memory Management failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Memory Management error: {str(e)}")
        return False

def test_api_compatibility() -> bool:
    """Test API compatibility with existing clients"""
    try:
        # Test GET search endpoint
        response = requests.get(
            f"{API_BASE_URL}/search",
            params={
                "q": "test query",
                "top_k": 3,
                "semantic_weight": 0.6,
                "lexical_weight": 0.4
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Compatibility: GET search works")
            print(f"   Results: {data.get('total_results', 0)}")
            return True
        else:
            print(f"❌ API Compatibility failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API Compatibility error: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 OpenSearch Migration Validation Tests")
    print("=" * 50)
    
    tests = [
        ("API Health", test_api_health),
        ("OpenSearch Health", test_opensearch_health),
        ("Search Functionality", test_search_functionality),
        ("Memory Management", test_memory_management),
        ("API Compatibility", test_api_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! OpenSearch migration successful.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
