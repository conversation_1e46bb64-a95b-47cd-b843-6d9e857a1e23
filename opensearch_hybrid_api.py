"""
OpenSearch-based Hybrid Search Engine API
Combines BM25 lexical search with vector semantic search for optimal results.
Single file implementation for clean deployment.
"""

print(f"\nInitializing OpenSearch Hybrid Search API server...", flush=True)

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, field_validator
from sentence_transformers import SentenceTransformer
from opensearchpy import OpenSearch, RequestsHttpConnection
from opensearchpy.helpers import bulk
from sqlalchemy import create_engine, text as sa_text
import connectorx as cx
import logging
import time
import traceback
from typing import Optional, List, Dict, Any
import uvicorn
from contextlib import asynccontextmanager
import numpy as np
import os
import json
import threading
import sys
import warnings

warnings.simplefilter(action="ignore", category=FutureWarning)

# Configuration for source database
srv_name2 = "HQDCSMOSQL01"
db_name2 = "PA_DEV2"
usr_name2 = "PAadmin"
pw2 = "PAadmin"
query_table = "ReportNLP"

# Connection URL for source database
connection_url2 = (
    "mssql+pyodbc://"
    f"{usr_name2}:{pw2}@{srv_name2}/{db_name2}"
    "?driver=ODBC+Driver+17+for+SQL+Server"
)

# OpenSearch configuration
OPENSEARCH_CONFIG = {
    'hosts': [{'host': 'localhost', 'port': 9200}],
    'http_compress': True,
    'use_ssl': False,  # Set to True if using https
    #'verify_certs': False,
    'connection_class': RequestsHttpConnection,
    'timeout': 30,
    'max_retries': 3,
    'retry_on_timeout': True,
    'http_auth': ("admin", "Summerist.l023")  # Add this line
}

# Index configuration
INDEX_NAME = "reportnlp_hybrid"
BATCH_SIZE = 2000
EMBEDDING_DIM = 384

# Global variables
model = None
engine = None
opensearch_client = None
index_exists = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('opensearch_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Pydantic models for API
class SearchRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    limit: Optional[int] = Field(default=10, ge=1, le=100, description="Number of results to return")
    min_score: Optional[float] = Field(default=0.0, ge=0.0, description="Minimum relevance score")
    semantic_weight: Optional[float] = Field(default=0.7, ge=0.0, le=1.0, description="Weight for semantic search (0-1)")
    lexical_weight: Optional[float] = Field(default=0.3, ge=0.0, le=1.0, description="Weight for lexical search (0-1)")
    
    @field_validator('semantic_weight', 'lexical_weight')
    @classmethod
    def validate_weights(cls, v, info):
        if 'semantic_weight' in info.data and 'lexical_weight' in info.data:
            if abs((info.data['semantic_weight'] + info.data['lexical_weight']) - 1.0) > 0.01:
                raise ValueError("semantic_weight + lexical_weight must equal 1.0")
        return v

class SearchResult(BaseModel):
    id: str
    score: float
    extracted_text: str
    metadata: Dict[str, Any]

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query_time_ms: float
    search_type: str

# OpenSearch index mapping
INDEX_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0,
        "analysis": {
            "analyzer": {
                "custom_text_analyzer": {
                    "type": "custom",
                    "tokenizer": "standard",
                    "filter": ["lowercase", "stop", "snowball"]
                }
            }
        },
        "index": {
            "knn": True
        }
    },
    "mappings": {
        "properties": {
            "id": {"type": "keyword"},
            "report_type": {"type": "keyword"},
            "niche_report_id": {"type": "keyword"},
            "entered_time": {"type": "date"},
            "report_time": {"type": "date"},
            "remarks": {"type": "text"},
            "niche_author_id": {"type": "keyword"},
            "niche_enter_id": {"type": "keyword"},
            "niche_occurrence_id": {"type": "keyword"},
            "occurrence_number": {"type": "keyword"},
            "occurrence_type": {"type": "keyword"},
            "zone": {"type": "keyword"},
            "team": {"type": "keyword"},
            "municipality": {"type": "keyword"},
            "fixed_type": {"type": "keyword"},
            "extracted_text": {
                "type": "text",
                "analyzer": "custom_text_analyzer",
                "fields": {
                    "keyword": {"type": "keyword"}
                }
            },
            "embedding_vector": {
                "type": "knn_vector",
                "dimension": EMBEDDING_DIM,
                "space_type": "cosinesimil"
            }
        }
    }
}

def initialize_opensearch():
    """Initialize OpenSearch client and create index if needed"""
    global opensearch_client, index_exists
    
    try:
        # Create OpenSearch client
        opensearch_client = OpenSearch(**OPENSEARCH_CONFIG)
        
        # Test connection
        cluster_info = opensearch_client.info()
        logger.info(f"Connected to OpenSearch cluster: {cluster_info['cluster_name']}")
        
        # Check if index exists
        if opensearch_client.indices.exists(index=INDEX_NAME):
            logger.info(f"Index '{INDEX_NAME}' already exists")
            index_exists = True
        else:
            # Create index
            logger.info(f"Creating index '{INDEX_NAME}'...")
            opensearch_client.indices.create(
                index=INDEX_NAME,
                body=INDEX_MAPPING
            )
            logger.info(f"Index '{INDEX_NAME}' created successfully")
            index_exists = True
            
    except Exception as e:
        logger.error(f"Failed to initialize OpenSearch: {str(e)}")
        raise

def load_and_index_data():
    """Load data from SQL Server and index into OpenSearch"""
    global model, engine
    
    try:
        if engine is None:
            logger.error("Database engine is not initialized.")
            raise RuntimeError("Database engine is not initialized.")
        if opensearch_client is None:
            logger.error("OpenSearch client is not initialized.")
            raise RuntimeError("OpenSearch client is not initialized.")
        logger.info("Starting FAST data indexing process...")

        # Get total count for chunking
        with engine.connect() as conn:
            result = conn.execute(sa_text("""
                SELECT COUNT(*) as total_count
                FROM ReportNLP
                WHERE empty_report = 0
            """))
            total_count = result.fetchone()[0]

        logger.info(f"Total records to process: {total_count}")

        # Check if index already has data
        index_stats = opensearch_client.indices.stats(index=INDEX_NAME)
        current_docs = index_stats['indices'][INDEX_NAME]['total']['docs']['count']
        if current_docs > 0:
            logger.info(f"Index already contains {current_docs:,} documents. Skipping indexing.")
            return

        CHUNK_SIZE = 5000
        processed = 0
        failed = 0

        for offset in range(0, total_count, CHUNK_SIZE):
            logger.info(f"Processing chunk: records {offset} - {min(offset+CHUNK_SIZE, total_count)}")
            query = f"""
                SELECT Id, Report_Type, Niche_Report_ID, Entered_Time, Report_Time, Remarks, 
                       Niche_Author_ID, Niche_Enter_ID, Niche_Occurrence_ID, Occurrence_Number, 
                       Occurrence_Type, Zone, Team, Municipality, fixed_type, extracted_text, embedding_vector
                FROM ReportNLP
                WHERE empty_report = 0
                ORDER BY Entered_Time
                OFFSET {offset} ROWS FETCH NEXT {CHUNK_SIZE} ROWS ONLY
            """
            df_chunk = cx.read_sql(connection_url2, query, return_type="pandas")
            if df_chunk.empty:
                continue

            valid_mask = df_chunk['embedding_vector'].notna() & (df_chunk['embedding_vector'] != '')
            df_valid = df_chunk[valid_mask].copy()
            documents = []
            for idx, row in df_valid.iterrows():
                try:
                    embedding_str = row['embedding_vector']
                    embedding = np.fromstring(embedding_str, sep=',', dtype=np.float32)
                    if len(embedding) != EMBEDDING_DIM or np.any(np.isnan(embedding)):
                        failed += 1
                        continue
                    # Use robust null-safe assignment for all fields except Id and embedding_vector
                    def safe_val(val):
                        if pd.isna(val) or (isinstance(val, str) and val.strip() == ""):
                            return None
                        return val
                    doc = {
                        "_index": INDEX_NAME,
                        "_id": str(row['Id']),
                        "_source": {
                            "id": str(row['Id']),
                            "report_type": safe_val(row.get('Report_Type')),
                            "niche_report_id": safe_val(row.get('Niche_Report_ID')),
                            "entered_time": row['Entered_Time'].isoformat() if pd.notna(row['Entered_Time']) and row['Entered_Time'] else None,
                            "report_time": row['Report_Time'].isoformat() if pd.notna(row['Report_Time']) and row['Report_Time'] else None,
                            "remarks": safe_val(row.get('Remarks')),
                            "niche_author_id": safe_val(row.get('Niche_Author_ID')),
                            "niche_enter_id": safe_val(row.get('Niche_Enter_ID')),
                            "niche_occurrence_id": safe_val(row.get('Niche_Occurrence_ID')),
                            "occurrence_number": safe_val(row.get('Occurrence_Number')),
                            "occurrence_type": safe_val(row.get('Occurrence_Type')),
                            "zone": safe_val(row.get('Zone')),
                            "team": safe_val(row.get('Team')),
                            "municipality": safe_val(row.get('Municipality')),
                            "fixed_type": safe_val(row.get('fixed_type')),
                            "extracted_text": safe_val(row.get('extracted_text')),
                            "embedding_vector": embedding.tolist()
                        }
                    }
                    documents.append(doc)
                except Exception as e:
                    failed += 1
                    continue
            # Bulk index documents in large chunks
            if documents:
                try:
                    success_count, failed_docs = bulk(
                        opensearch_client,
                        documents,
                        index=INDEX_NAME,
                        chunk_size=1000
                    )
                    processed += success_count
                    if failed_docs:
                        failed += len(failed_docs)
                except Exception as e:
                    logger.error(f"Bulk indexing failed for chunk: {str(e)}")
                    failed += len(documents)
            logger.info(f"Progress: {processed:,} indexed, {failed:,} failed")
        logger.info(f"Indexing completed: {processed:,} documents indexed, {failed:,} failed")
        opensearch_client.indices.refresh(index=INDEX_NAME)
    except Exception as e:
        logger.error(f"Data indexing failed: {str(e)}")
        raise

def perform_hybrid_search(query: str, limit: int = 10, semantic_weight: float = 0.7, 
                         lexical_weight: float = 0.3, min_score: float = 0.0):
    """Perform hybrid search combining BM25 and vector similarity"""
    
    try:
        if model is None:
            logger.error("SentenceTransformer model is not initialized.")
            raise RuntimeError("SentenceTransformer model is not initialized.")
        if opensearch_client is None:
            logger.error("OpenSearch client is not initialized.")
            raise RuntimeError("OpenSearch client is not initialized.")
        # Generate query embedding
        query_embedding = model.encode(query).tolist()
        # Build hybrid search query
        search_body = {
            "size": limit,
            "min_score": min_score,
            "query": {
                "hybrid": {
                    "queries": [
                        {
                            "match": {
                                "extracted_text": {
                                    "query": query,
                                    "boost": lexical_weight
                                }
                            }
                        },
                        {
                            "knn": {
                                "embedding_vector": {
                                    "vector": query_embedding,
                                    "k": limit * 2,
                                    "boost": semantic_weight
                                }
                            }
                        }
                    ]
                }
            },
            "_source": {
                "excludes": ["embedding_vector"]
            }
        }
        # Execute search
        response = opensearch_client.search(
            index=INDEX_NAME,
            body=search_body
        )
        return response
    except Exception as e:
        logger.error(f"Hybrid search failed: {str(e)}")
        # Fallback to simple match query
        fallback_body = {
            "size": limit,
            "query": {
                "match": {
                    "extracted_text": query
                }
            },
            "_source": {
                "excludes": ["embedding_vector"]
            }
        }
        response = opensearch_client.search(
            index=INDEX_NAME,
            body=fallback_body
        )
        return response

# API startup and shutdown
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application startup and shutdown lifecycle"""
    global model, engine
    
    try:
        # Startup
        logger.info("Starting OpenSearch Hybrid Search API...")
        
        # Initialize sentence transformer
        logger.info("Loading SentenceTransformer model...")
        model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        logger.info("SentenceTransformer model loaded successfully")
        
        # Initialize database connection
        logger.info("Initializing database connection...")
        engine = create_engine(connection_url2, pool_pre_ping=True)
        logger.info("Database connection established")
        
        # Initialize OpenSearch
        initialize_opensearch()
        
        # Load and index data if needed
        load_and_index_data()
        
        logger.info("OpenSearch Hybrid Search API startup completed successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"API startup failed: {str(e)}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down OpenSearch Hybrid Search API...")
        if opensearch_client:
            opensearch_client.close()
        if engine:
            engine.dispose()
        logger.info("API shutdown completed")

# Create FastAPI app
app = FastAPI(
    title="OpenSearch Hybrid Search API",
    description="High-performance hybrid search combining BM25 lexical and vector semantic search",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Routes
@app.get("/")
async def root():
    """API health check"""
    return {"message": "OpenSearch Hybrid Search API", "status": "running"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    try:
        # Check OpenSearch connection
        cluster_health = opensearch_client.cluster.health()
        
        # Check index status
        index_stats = opensearch_client.indices.stats(index=INDEX_NAME)
        doc_count = index_stats['indices'][INDEX_NAME]['total']['docs']['count']
        
        return {
            "status": "healthy",
            "opensearch_status": cluster_health['status'],
            "index_name": INDEX_NAME,
            "document_count": doc_count,
            "model_loaded": model is not None,
            "database_connected": engine is not None
        }
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

@app.post("/search", response_model=SearchResponse)
async def search(request: SearchRequest):
    """Perform hybrid search"""
    try:
        start_time = time.time()
        
        # Validate OpenSearch and model
        if opensearch_client is None or not index_exists:
            raise HTTPException(status_code=503, detail="OpenSearch not available")
        if model is None:
            raise HTTPException(status_code=503, detail="Model not loaded")
        # Use default values if any are None
        response = perform_hybrid_search(
            query=request.query,
            limit=request.limit if request.limit is not None else 10,
            semantic_weight=request.semantic_weight if request.semantic_weight is not None else 0.7,
            lexical_weight=request.lexical_weight if request.lexical_weight is not None else 0.3,
            min_score=request.min_score if request.min_score is not None else 0.0
        )
        
        # Process results
        results = []
        for hit in response['hits']['hits']:
            source = hit['_source']
            
            result = SearchResult(
                id=source['id'],
                score=hit['_score'],
                extracted_text=source['extracted_text'],
                metadata={
                    'report_type': source.get('report_type'),
                    'niche_report_id': source.get('niche_report_id'),
                    'entered_time': source.get('entered_time'),
                    'report_time': source.get('report_time'),
                    'remarks': source.get('remarks'),
                    'niche_author_id': source.get('niche_author_id'),
                    'niche_enter_id': source.get('niche_enter_id'),
                    'niche_occurrence_id': source.get('niche_occurrence_id'),
                    'occurrence_number': source.get('occurrence_number'),
                    'occurrence_type': source.get('occurrence_type'),
                    'zone': source.get('zone'),
                    'team': source.get('team'),
                    'municipality': source.get('municipality'),
                    'fixed_type': source.get('fixed_type')
                }
            )
            results.append(result)
        
        query_time = (time.time() - start_time) * 1000
        
        return SearchResponse(
            results=results,
            total=response['hits']['total']['value'],
            query_time_ms=round(query_time, 2),
            search_type="hybrid"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Search failed: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.post("/search/semantic", response_model=SearchResponse)
async def semantic_search(request: SearchRequest):
    """Perform semantic (vector) search only"""
    try:
        start_time = time.time()
        
        if opensearch_client is None or not index_exists:
            raise HTTPException(status_code=503, detail="OpenSearch not available")
        if model is None:
            raise HTTPException(status_code=503, detail="Model not loaded")
        # Use default values if any are None
        limit = request.limit if request.limit is not None else 10
        min_score = request.min_score if request.min_score is not None else 0.0
        query_embedding = model.encode(request.query).tolist()
        search_body = {
            "size": limit,
            "min_score": min_score,
            "query": {
                "knn": {
                    "embedding_vector": {
                        "vector": query_embedding,
                        "k": limit
                    }
                }
            },
            "_source": {
                "excludes": ["embedding_vector"]
            }
        }
        response = opensearch_client.search(
            index=INDEX_NAME,
            body=search_body
        )
        
        # Process results
        results = []
        for hit in response['hits']['hits']:
            source = hit['_source']
            
            result = SearchResult(
                id=source['id'],
                score=hit['_score'],
                extracted_text=source['extracted_text'],
                metadata={
                    'report_type': source.get('report_type'),
                    'niche_report_id': source.get('niche_report_id'),
                    'entered_time': source.get('entered_time'),
                    'report_time': source.get('report_time'),
                    'remarks': source.get('remarks'),
                    'niche_author_id': source.get('niche_author_id'),
                    'niche_enter_id': source.get('niche_enter_id'),
                    'niche_occurrence_id': source.get('niche_occurrence_id'),
                    'occurrence_number': source.get('occurrence_number'),
                    'occurrence_type': source.get('occurrence_type'),
                    'zone': source.get('zone'),
                    'team': source.get('team'),
                    'municipality': source.get('municipality'),
                    'fixed_type': source.get('fixed_type')
                }
            )
            results.append(result)
        
        query_time = (time.time() - start_time) * 1000
        
        return SearchResponse(
            results=results,
            total=response['hits']['total']['value'],
            query_time_ms=round(query_time, 2),
            search_type="semantic"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Semantic search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Semantic search failed: {str(e)}")

@app.post("/search/lexical", response_model=SearchResponse)
async def lexical_search(request: SearchRequest):
    """Perform lexical (BM25) search only"""
    try:
        start_time = time.time()
        
        if opensearch_client is None or not index_exists:
            raise HTTPException(status_code=503, detail="OpenSearch not available")
        # Use default values if any are None
        limit = request.limit if request.limit is not None else 10
        min_score = request.min_score if request.min_score is not None else 0.0
        search_body = {
            "size": limit,
            "min_score": min_score,
            "query": {
                "match": {
                    "extracted_text": {
                        "query": request.query,
                        "operator": "or"
                    }
                }
            },
            "_source": {
                "excludes": ["embedding_vector"]
            }
        }
        response = opensearch_client.search(
            index=INDEX_NAME,
            body=search_body
        )
        
        # Process results
        results = []
        for hit in response['hits']['hits']:
            source = hit['_source']
            
            result = SearchResult(
                id=source['id'],
                score=hit['_score'],
                extracted_text=source['extracted_text'],
                metadata={
                    'report_type': source.get('report_type'),
                    'niche_report_id': source.get('niche_report_id'),
                    'entered_time': source.get('entered_time'),
                    'report_time': source.get('report_time'),
                    'remarks': source.get('remarks'),
                    'niche_author_id': source.get('niche_author_id'),
                    'niche_enter_id': source.get('niche_enter_id'),
                    'niche_occurrence_id': source.get('niche_occurrence_id'),
                    'occurrence_number': source.get('occurrence_number'),
                    'occurrence_type': source.get('occurrence_type'),
                    'zone': source.get('zone'),
                    'team': source.get('team'),
                    'municipality': source.get('municipality'),
                    'fixed_type': source.get('fixed_type')
                }
            )
            results.append(result)
        
        query_time = (time.time() - start_time) * 1000
        
        return SearchResponse(
            results=results,
            total=response['hits']['total']['value'],
            query_time_ms=round(query_time, 2),
            search_type="lexical"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Lexical search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Lexical search failed: {str(e)}")

@app.get("/stats")
async def get_stats():
    """Get search engine statistics"""
    try:
        if opensearch_client is None or not index_exists:
            raise HTTPException(status_code=503, detail="OpenSearch not available")
        # Get cluster and index stats
        cluster_health = opensearch_client.cluster.health()
        index_stats = opensearch_client.indices.stats(index=INDEX_NAME)
        index_info = index_stats['indices'][INDEX_NAME]
        return {
            "cluster_name": cluster_health['cluster_name'],
            "cluster_status": cluster_health['status'],
            "index_name": INDEX_NAME,
            "document_count": index_info['total']['docs']['count'],
            "index_size_bytes": index_info['total']['store']['size_in_bytes'],
            "index_size_mb": round(index_info['total']['store']['size_in_bytes'] / (1024 * 1024), 2),
            "model_name": "sentence-transformers/all-MiniLM-L6-v2",
            "embedding_dimension": EMBEDDING_DIM
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Stats retrieval failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Stats retrieval failed: {str(e)}")

# Add import for pandas
import pandas as pd

if __name__ == "__main__":
    uvicorn.run(
        "opensearch_hybrid_api:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
