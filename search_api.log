2025-08-06 09:14:01,380 - __main__ - INFO - Starting up Search API...
2025-08-06 09:14:01,380 - __main__ - INFO - Loading SentenceTransformer model...
2025-08-06 09:14:01,382 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-08-06 09:14:01,382 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-06 09:14:03,137 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-08-06 09:14:04,703 - __main__ - INFO - Model loaded successfully
2025-08-06 09:14:04,704 - __main__ - INFO - Initializing database connection...
2025-08-06 09:14:04,784 - __main__ - INFO - Database connection established
2025-08-06 09:14:04,785 - __main__ - INFO - Loading search cache on disk...
2025-08-06 09:14:04,785 - __main__ - INFO - Cache file missing: search_cache/embeddings.dat
2025-08-06 09:14:04,785 - __main__ - INFO - Cache files not found, generating new cache from database...
2025-08-06 09:14:04,786 - __main__ - INFO - Loading data with chunked processing and splitting...
2025-08-06 09:14:06,630 - __main__ - INFO - Total records to process: 1335776
2025-08-06 09:14:06,637 - __main__ - INFO - Processing chunk 1: records 0-10000
2025-08-06 09:14:36,949 - __main__ - INFO - Progress: 10000/1335776 records processed (0.7%)
2025-08-06 09:14:36,949 - __main__ - INFO - Processing chunk 2: records 10000-20000
2025-08-06 09:16:00,093 - __main__ - INFO - Processing chunk 3: records 20000-30000
2025-08-07 09:12:21,704 - __main__ - INFO - Starting up OpenSearch Hybrid Search API...
2025-08-07 09:12:21,706 - __main__ - INFO - Loading SentenceTransformer model...
2025-08-07 09:12:21,708 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-08-07 09:12:21,708 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-07 09:12:23,584 - __main__ - INFO - Model loaded successfully
2025-08-07 09:12:23,584 - __main__ - INFO - Initializing database connection...
2025-08-07 09:12:23,679 - __main__ - INFO - Database connection established
2025-08-07 09:12:23,680 - __main__ - INFO - Initializing OpenSearch connection...
2025-08-07 09:12:23,688 - opensearch - INFO - GET http://localhost:9200/ [status:200 request:0.007s]
2025-08-07 09:12:23,689 - __main__ - INFO - Connected to OpenSearch cluster: opensearch-cluster
2025-08-07 09:12:23,696 - opensearch - INFO - HEAD http://localhost:9200/reportnlp_hybrid [status:200 request:0.007s]
2025-08-07 09:12:23,696 - __main__ - INFO - Index 'reportnlp_hybrid' already exists
2025-08-07 09:12:23,696 - __main__ - INFO - OpenSearch connection established
2025-08-07 09:12:23,698 - __main__ - INFO - Checking OpenSearch index data...
2025-08-07 09:12:23,698 - __main__ - INFO - Starting data indexing to OpenSearch...
2025-08-07 09:12:25,604 - __main__ - INFO - Total records to process: 1,339,515
2025-08-07 09:12:25,611 - opensearch - INFO - HEAD http://localhost:9200/reportnlp_hybrid [status:200 request:0.006s]
2025-08-07 09:12:25,628 - opensearch - INFO - GET http://localhost:9200/reportnlp_hybrid/_stats [status:200 request:0.017s]
2025-08-07 09:12:25,629 - __main__ - ERROR - Data indexing to OpenSearch failed: 'reportnlp_hybrid'
2025-08-07 09:12:25,629 - __main__ - ERROR - Startup failed: 'reportnlp_hybrid'
